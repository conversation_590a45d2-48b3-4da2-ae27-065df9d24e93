import argparse
import os
from cv_batch.get_results import get_batch_job_results, insert_to_es, api_insert_to_es
from services.redis.redis import rd

from cv_batch.main import main as execute_cv_batch
from screening_batch.main import main as execute_screening_batch


def batch_action_selector(action):
    parser_selector = {"cv": execute_cv_batch, "screening": execute_screening_batch}

    return parser_selector.get(action, execute_cv_batch)


def get_remaining_results(batch_job_ids, redis_key):
    while batch_job_ids:
        for batch_job_id in batch_job_ids[:]:
            results = get_batch_job_results(batch_job_id)
            if results:
                insert_to_es(results)
                batch_job_ids.remove(batch_job_id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        print(f"Number of remaining batches: {len(batch_job_ids)}")
        time.sleep(15)

def main(args):
    batch_action_selector(args.Action_Type)(args)

if __name__ == "__main__":
    BASE_PATH = os.path.dirname(os.path.abspath(__file__))
    batch_files_folder = os.path.join(BASE_PATH, "batch-files")
    downloads_folder = os.path.join(BASE_PATH, "downloads")
    results_folder = os.path.join(BASE_PATH, "results")
    file_contents_folder = os.path.join(BASE_PATH, "file_contents")

    os.makedirs(batch_files_folder, exist_ok=True)
    os.makedirs(results_folder, exist_ok=True)
    os.makedirs(file_contents_folder, exist_ok=True)
    os.makedirs(downloads_folder, exist_ok=True)
    

    parser = argparse.ArgumentParser()
    parser.add_argument("-a", "--Action_Type", required=True, help="Action Type")
    parser.add_argument("-b", "--Batch_Size", default=30, help="Batch Size")
    parser.add_argument("-r", "--Resumed_Run", required=True, help="Resumed Run")
    parser.add_argument("-t", "--Test", required=True, help="Test")
    parser.add_argument("-api", "--API", default=0, help="API")
    args = parser.parse_args()

    main(args)
