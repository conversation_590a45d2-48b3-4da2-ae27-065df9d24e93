[tool.poetry]
name = "batch-processing-openai"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pymysql = "^1.1.1"
requests = "^2.32.3"
python-dotenv = "^1.0.1"
elasticsearch = "^8.15.0"
openai = "^1.40.6"
phonenumbers = "^8.13.43"
python-dateutil = "^2.9.0.post0"
langchain = "^0.2.14"
tiktoken = "^0.7.0"
redis = "^5.0.8"
streamlit = "^1.37.1"
pandas = "^2.2.2"
dbutils = "^3.1.0"
jinja2 = "^3.1.4"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
