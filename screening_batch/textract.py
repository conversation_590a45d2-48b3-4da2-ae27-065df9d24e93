from base64 import b64encode
import requests, os


url = os.getenv("TEXTRACT_API")
f = "downloads/5600706_8037286.pdf"

with open(f, "rb") as open_file:
    byte_content = open_file.read()
    base64_bytes = b64encode(byte_content)
    base64_string = base64_bytes.decode("utf-8")
    raw_data = {
        "data": base64_string,
        "file_type": f.split(".")[-1],
    }

r = requests.post(url, json=raw_data)
print(r.status_code)
print(r.json()["text"])
