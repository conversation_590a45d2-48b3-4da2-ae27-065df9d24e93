import glob
import json
import os
import time
from concurrent.futures import ThreadPoolExecutor

from services.redis.redis import rd

from .helpers import (
    create_batch_files,
    create_batch_job,
    get_batch_job_results,
    get_fake_resumes,
    get_resumes,
    insert_to_es,
    process_resume,
)


def remove_all_files(folder):
    for file in glob.glob(os.path.join(folder, "*")):
        try:
            os.remove(file)
        except Exception as e:
            print(f"Error removing {file}: {e}")


def process_batches_parallel(resume_batches, redis_key, glob_path, max_files):
    batch_job_ids = []
    remove_all_files("screening_batch/contents")
    for batch in resume_batches:
        with ThreadPoolExecutor() as resume_executor:
            resume_futures = []

            for resume in batch:
                # Process each resume in parallel
                resume_futures.append(resume_executor.submit(process_resume, resume))

            # Wait for all resume futures to complete
            for future in resume_futures:
                future.result()  # Raise exceptions if any occurred during resume processing
        list_batch_file = create_batch_files(glob_path, max_files)
        for batch_file in list_batch_file:
            batch_job = create_batch_job(batch_file.id)
            batch_job_ids.append(batch_job.id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        remove_all_files("screening_batch/downloads")
        remove_all_files("screening_batch/batch-files")
        remove_all_files("screening_batch/contents")
    return batch_job_ids


def get_remaining_results(batch_job_ids, redis_key):
    while batch_job_ids:
        for batch_job_id in batch_job_ids[:]:
            results = get_batch_job_results(batch_job_id)
            if results:
                insert_to_es(batch_job_id, results)
                batch_job_ids.remove(batch_job_id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        print(f"Number of remaining batches: {len(batch_job_ids)}")
        time.sleep(15)


def main(args):
    redis_key = "screening_batch_processing"
    glob_path = "screening_batch/contents/*.*"

    if int(args.Resumed_Run) == 0:
        batch_size = int(args.Batch_Size)
        resume_batches = (
            get_fake_resumes(batch_size)
            if int(args.Test) == 1
            else get_resumes(batch_size)
        )
        batch_job_ids = process_batches_parallel(
            resume_batches, redis_key, glob_path, batch_size
        )
        print("Push Job Done")
    else:
        batch_job_ids = json.loads(rd.get(redis_key) or "[]")

    get_remaining_results(batch_job_ids, redis_key)
    print("Get Results Done")
