from typing import List, Optional

from pydantic import BaseModel


class Experience(BaseModel):
    jobtitle: Optional[str] = "Unknown Job Title"
    companyname: Optional[str] = "Unknown Company"
    description: Optional[str] = "No description provided"
    startdate: Optional[str] = "Not specified"
    enddate: Optional[str] = "Not specified"


class Education(BaseModel):
    highestdegreename: Optional[str] = "Unknown Degree"
    schoolname: Optional[str] = "Unknown School"
    major: Optional[str] = "Unknown Major"
    startdate: Optional[str] = "Not specified"
    enddate: Optional[str] = "Not specified"
    description: Optional[str] = "No description provided"


class Skill(BaseModel):
    skillName: Optional[str] = "Unknown Skill"
    rating: Optional[int] = 0


class Certificate(BaseModel):
    name: Optional[str] = "Unknown Certificate"
    organization: Optional[str] = "Unknown Organization"
    fromDate: Optional[str] = "1970-01-01"
    linkCertification: Optional[str] = None


class CandidateData(BaseModel):
    full_name: Optional[str] = "Unknown Name"
    job_title: Optional[str] = "Unknown Job Title"
    skills: List[Skill] = []
    experience: List[Experience] = []
    education: List[Education] = []
    certificates: List[Certificate] = []
    salary: Optional[str] = "Not specified"
    job_function: Optional[str] = "Not specified"
    industries: Optional[str] = "Not specified"
    locations: Optional[str] = "Not specified"
    age: Optional[str] = "Unknown"
    gender: Optional[str] = "Not specified"
    marital_status: Optional[str] = "Not specified"
