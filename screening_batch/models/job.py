from typing import Optional

from pydantic import BaseModel


class Job(BaseModel):
    job_title: Optional[str] = "Unknown Job Title"
    job_level: Optional[str] = "Unknown Job Level"
    job_description: Optional[str] = "No description provided"
    job_requirements: Optional[str] = "No requirements specified"
    salary: Optional[str] = "No salary provided"
    job_function: Optional[str] = "No job function provided"
    company_industry: Optional[str] = "No industry provided"
    locations: Optional[str] = "No location provided"
