from typing import List, Optional

from pydantic import BaseModel, Field
from pydantic.v1 import validator


class MultilingualText(BaseModel):
    en: str = Field(description="English text")
    vi: str = Field(description="Vietnamese text")


class Reasoning(BaseModel):
    strengths: List[MultilingualText] = Field(
        default_factory=list,
        description="a list of up to 5 strengths of the candidate from the evaluation",
    )
    weaknesses: List[MultilingualText] = Field(
        default_factory=list,
        description="a list of up to 5 weaknesses of the candidate from the evaluation",
    )

    @validator("strengths", pre=True)
    def validate_strengths(cls, field):
        if not isinstance(field, list):
            return []
        return field

    @validator("weaknesses", pre=True)
    def validate_weaknesses(cls, field):
        if not isinstance(field, list):
            return []
        return field


class BriefEvaluation(BaseModel):
    brief_evaluation: Reasoning = Field(
        description="provide evaluation for each criterion"
    )


class LocationInformation(BaseModel):
    candidate_home_city: str | None = Field(
        default=None,
        description="the candidate's home city. Return null if not mentioned",
    )
    desired_job_locations: List[str] = Field(
        description="a list of the candidate's desired/expected job location cities and cities in which they are looking for jobs. Return an empty list if not mentioned",
    )
    job_locations: List[str] = Field(
        description="a list of all cities where the job is based. Return an empty list if not mentioned"
    )


class LocationMatching(LocationInformation):
    is_matching: Optional[bool] = Field(
        True,
        description="""
        Evaluate if the candidate's location preferences align with the job's working locations, following these guidelines:
        Return true if the candidate's home city or any of their desired job locations matches or is included in the job's listed cities. Else return false.
        """,
    )
