{"mappings": {"properties": {"@timestamp": {"type": "date"}, "result": {"properties": {"activities": {"properties": {"description": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "from": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "organizer": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "position": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "to": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "basic_info": {"properties": {"current_job_function": {"type": "long"}, "current_job_level": {"type": "long"}, "highest_education": {"type": "long"}, "years_of_experience": {"type": "long"}}}, "certificates": {"properties": {"link": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "organizer": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "year": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "contact_info": {"properties": {"address": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "address_city": {"type": "long"}, "address_district": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "birthday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "email": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "gender": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "job_title": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "linkedin": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "marriage_status": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "phone": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "educations": {"properties": {"degree": {"type": "long"}, "description": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "from": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "gpa": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "major": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "to": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "university": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "languages": {"properties": {"name": {"type": "long"}, "rating": {"type": "long"}}}, "projects": {"properties": {"description": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "from": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "to": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "references": {"properties": {"company": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "email": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "full_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "phone": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "position": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "skills": {"properties": {"level": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "year_of_experience": {"type": "long"}}}, "summary": {"properties": {"objectives": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "working_histories": {"properties": {"company": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "description": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "from": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "title": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "to": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "duration": {"type": "long"}}}, "working_preference": {"properties": {"expected_industry": {"type": "long"}, "expected_job_function": {"type": "long"}, "expected_job_level": {"type": "long"}, "expected_location": {"type": "long"}, "expected_salary": {"type": "long"}, "unit": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}}, "resumeId": {"type": "long"}, "userId": {"type": "long"}}}}