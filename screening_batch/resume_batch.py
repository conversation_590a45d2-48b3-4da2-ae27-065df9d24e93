import json
import os
import sys

from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY_SCREENING"))


def resume_batch(batch_job_id):
    print(f"Checking...Batch job id {batch_job_id}")
    results = []
    batch_job_retrieve = client.batches.retrieve(batch_job_id)
    if batch_job_retrieve.status == "completed":
        print(f"Batch job id {batch_job_retrieve.id} is completed")
        result_file_id = (
            batch_job_retrieve.output_file_id or batch_job_retrieve.error_file_id
        )
        result = client.files.content(result_file_id).content
        print(result)
        result_path = os.path.join("screening_batch/results", f"{batch_job_retrieve.id}.jsonl")
        with open(result_path, "wb") as file:
            file.write(result)

        with open(result_path, "r") as file:
            results.extend(json.loads(line.strip()) for line in file)
    else:
        print(f"Batch job id {batch_job_id} is not completed yet!")
        return None

    return results

if __name__ == "__main__":
    batch_ids = sys.argv[1]  # Get all batch IDs from command line arguments
    resume_batch(batch_ids)
