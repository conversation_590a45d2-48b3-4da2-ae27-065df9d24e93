from screening_batch.models.job import Job
from services.mysql.mysql import execute_query_with_dict_cursor


def fetch_job_information(job_id):

    # Optimized queries to minimize the number of database calls
    query = f"""
    SELECT 
        j.jobtitle as jobTitle, j.jobdescription as jobDescription, 
        j.skillexperience as jobRequirements, jl.joblevelname as jobLevel,
        tri.industryV3Name as companyIndustry, trjf.jobFunctionV3Name as jobFunction,
        GROUP_CONCAT(DISTINCT trl.locationName) as workingLocation,
        cur.salaryCurrency, j.salarymin, j.salarymax
    FROM tbljob j
    LEFT JOIN tblref_joblevel_brief jl ON j.joblevelid = jl.joblevelid AND jl.languageid = 2
    LEFT JOIN tbljob_extra_info ei ON j.jobId = ei.jobId
    LEFT JOIN tblref_industry_v3 tri ON ei.industryV3Id = tri.industryV3Id AND tri.languageCode = 'en'
    LEFT JOIN tblref_job_function_v3 trjf ON ei.jobFunctionV3Id = trjf.jobFunctionV3Id AND trjf.languageCode = 'en'
    LEFT JOIN tblref_location_v2 trl ON trl.translateLocationId IN (
        SELECT cityid FROM tbljob_city WHERE jobid = '{job_id}'
    ) AND trl.languageCode = 'en'
    LEFT JOIN tblref_salary_currency cur ON cur.salaryCurrencyId = ei.salaryCurrencyId
    WHERE j.jobId = '{job_id}'
    GROUP BY j.jobId;
    """

    job_data = execute_query_with_dict_cursor(query)

    if not job_data:
        return {}

    job_data = job_data[0]

    # Create a Job object
    job_info = Job(
        job_title=job_data.get("jobTitle", None),
        job_level=job_data.get("jobLevel", None),
        job_description=job_data.get("jobDescription", None),
        job_requirements=job_data.get("jobRequirements", None),
        salary=(
            f"{job_data.get('salaryCurrency', '')} {job_data.get('salarymin', '')}-{job_data.get('salarymax', '')}"
            if job_data.get("salaryCurrency")
            else None
        ),
        job_function=job_data.get("jobFunction", None),
        company_industry=job_data.get("companyIndustry", None),
        locations=job_data.get("workingLocation", None),
    )

    return job_info
