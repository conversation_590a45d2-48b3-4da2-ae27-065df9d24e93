import asyncio
import os
import time
from datetime import datetime

import requests

from screening_batch.models.candidate import (
    CandidateData,
    Certificate,
    Education,
    Experience,
    Skill,
)
from screening_batch.utils import get_content, load_template, render_candidate_template
from services.mysql.mysql import execute_query_with_dict_cursor, get_connection, pool


def get_resumes(batch_size=30):
    try:
        query = """
        SELECT jobId, jobAppId, jsUserId as userId, resumeId, fileorigin 
        FROM in_inter.tblapplication_batch_ai_screening;
        """
        session = get_connection(slave=True)
        cursor = session.cursor()
        cursor.execute(query)

        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break
            yield rows

        cursor.close()
        session.close()

    except Exception as e:
        print("Error: ", e)


def get_fake_resumes(batch_size):
    image_url = os.getenv("IMAGE_SERVER")

    try:
        query = f"""
        select a.jobId, a.jobAppId, a.userId, a.resumeId, 
        CONCAT('{image_url}/resumes_sentdirectly/',r.folderName,'/',r.physicalFileApp,'.',r.extension) AS fileorigin
        from tbljob_application a
        left join tblresume_attachment r on a.resumeId = r.resumeid
        where a.createdOn >= '2024-07-01' and a.createdOn < '2024-08-01'
        order by createdOn asc
        limit 20;
        """
        session = get_connection(write=True)

        cursor = session.cursor()
        cursor.execute(query)

        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break
            yield rows

        cursor.close()
        session.close()
    except Exception as e:
        print("Error: ", e)


def download_resumes(resume):
    job_id, job_app_id, user_id, resume_id, link = resume
    filename = os.path.basename(link)
    extension = os.path.splitext(filename)[-1]
    os.makedirs(os.path.join("screening_batch", "downloads"), exist_ok=True)
    download_file_path = os.path.join(
        "screening_batch",
        "downloads",
        f"{job_id}_{job_app_id}_{user_id}_{resume_id}{extension}",
    )
    response = requests.get(link)
    if response.status_code == 200:
        with open(download_file_path, "wb") as f:
            f.write(response.content)
            return download_file_path
    else:
        print("resume_id:", resume_id)
        print("Status_code:", response.status_code)
        return None


def process_resume(resume):
    job_id, job_app_id, user_id, resume_id, link = resume
    os.makedirs(os.path.join("screening_batch", "contents"), exist_ok=True)

    content_file = os.path.join(
        "screening_batch",
        "contents",
        f"{job_id}_{job_app_id}_{user_id}_{resume_id}.txt",
    )

    if link:
        downloaded_file_path = download_resumes(resume)
        content = get_content(downloaded_file_path)

    if not link or content == "":
        candidate_data = fetch_candidate_data(resume_id, user_id)
        template_path = os.path.join(
            "screening_batch", "template", "candidate", "candidate.jinja"
        )
        candidate_template_str = load_template(template_path)
        content = render_candidate_template(candidate_data, candidate_template_str)
    with open(content_file, "w") as f:
        f.write(str(content))

    print("Done processing resumes | AppId:", job_app_id, time.time())


def process_resumes(resumes):
    for resume in resumes:
        process_resume(resume)


# Function to fetch candidate data
def fetch_candidate_data(resume_id, user_id):
    # Define individual queries
    query_candidate = f"SELECT fullname, birthday, desiredjobtitle, suggestedsalary, genderid, maritalstatusid, resumeid FROM tblresume WHERE resumeid = {resume_id};"
    query_experience = f"""
    SELECT ex.jobtitle, 
           (CASE
                WHEN cp.companyName != '' THEN cp.companyName
                WHEN ec.companyname != '' THEN ec.companyname
                ELSE ex.companyname
            END) AS companyname, 
           ex.description, 
           ex.startdate, 
           ex.enddate 
    FROM tblresume_experience ex
    LEFT JOIN tblemployer_companyinfo ec ON ec.companyid = ex.companyId
    LEFT JOIN tblcompany_profile cp ON ec.companyid = cp.companyId AND cp.status = 1 AND cp.coProfileType = 1
    WHERE ex.resumeid = {resume_id}
    GROUP BY ex.entryid;
    """
    query_education = f"""
    SELECT rh.highestdegreename, schoolname, major, startdate, enddate, description 
    FROM tblresume_education re
    INNER JOIN tblref_highestdegree rh ON re.highestdegreeid = rh.highestdegreeid
    WHERE resumeid = {resume_id} AND rh.languageid = 2;
    """
    query_skills = f"""
    SELECT skillName, rating 
    FROM tbljobseeker_skill_term jkt
    INNER JOIN tblskill_term st ON jkt.skillId = st.skillId
    WHERE userid = {user_id};
    """
    query_certificates = f"""
    SELECT name, 
           (CASE WHEN organization = '' THEN co.organizationName ELSE organization END) as organization, 
           fromDate, 
           linkCertification 
    FROM tbluser_certification c
    LEFT JOIN tblref_certification_organization co ON c.organizationId = co.organizationId
    WHERE userId = {user_id};
    """
    query_job_function = f"""
    SELECT GROUP_CONCAT(rjf.jobFunctionV3Name ORDER BY rjf.jobFunctionV3Name SEPARATOR ', ') as job_function
    FROM tbluser_extra_information uei
    INNER JOIN tblref_job_function_v3 rjf ON uei.jobFunctionV3Id = rjf.jobFunctionV3Id
    WHERE userId = {user_id} AND rjf.languageCode = "en";
    """
    query_locations = f"""
    SELECT GROUP_CONCAT(DISTINCT rloc.locationName ORDER BY rloc.locationName SEPARATOR ', ') as workingLocation 
    FROM tbluser_expected_location uel
    LEFT JOIN tblref_location_v2 rloc ON uel.expectedCityId = rloc.translateLocationId AND rloc.languageCode  = 'en'
    WHERE userId = {user_id};
    """
    query_industries = f"""
    SELECT GROUP_CONCAT(DISTINCT ri.industryV3Name ORDER BY ri.industryV3Name SEPARATOR ', ') as industryName  
    FROM tblresume_industry_v3 re
    INNER JOIN tblref_industry_v3 ri ON re.industryV3Id = ri.industryV3Id AND ri.languageCode = 'en'
    WHERE resumeId = {resume_id};
    """

    # Run queries
    candidate_data = execute_query_with_dict_cursor(query_candidate)
    experience_data = execute_query_with_dict_cursor(query_experience)
    education_data = execute_query_with_dict_cursor(query_education)
    skills_data = execute_query_with_dict_cursor(query_skills)
    certificates_data = execute_query_with_dict_cursor(query_certificates)
    job_function = execute_query_with_dict_cursor(query_job_function)
    locations = execute_query_with_dict_cursor(query_locations)
    industries = execute_query_with_dict_cursor(query_industries)

    if not candidate_data:
        return {}

    candidate_data = candidate_data[0]

    # Convert datetime.date to string for education data
    def convert_date_to_str(date):
        if date is None:
            return ""
        return date if isinstance(date, str) else date.strftime("%Y-%m-%d")

    # Create CandidateData object
    candidate = CandidateData(
        full_name=candidate_data.get("fullname"),
        job_title=candidate_data.get("desiredjobtitle"),
        skills=[Skill(**skill) for skill in skills_data],
        experience=[
            Experience(
                jobtitle=exp["jobtitle"],
                companyname=exp["companyname"],
                description=exp["description"],
                startdate=convert_date_to_str(exp["startdate"]),
                enddate=convert_date_to_str(exp["enddate"]),
            )
            for exp in experience_data
        ],
        education=[
            Education(
                highestdegreename=edu["highestdegreename"],
                schoolname=edu["schoolname"],
                major=edu["major"],
                startdate=convert_date_to_str(edu["startdate"]),
                enddate=convert_date_to_str(edu["enddate"]),
                description=edu["description"],
            )
            for edu in education_data
        ],
        certificates=[
            Certificate(
                name=cert["name"],
                organization=cert["organization"],
                fromDate=convert_date_to_str(cert["fromDate"]),
                linkCertification=cert["linkCertification"],
            )
            for cert in certificates_data
        ],
        salary=str(candidate_data.get("suggestedsalary")),
        job_function=job_function[0].get("job_function"),
        industries=industries[0].get("industryName"),
        locations=locations[0].get("workingLocation"),
        age=calculate_age(candidate_data.get("birthday")),
        gender=get_gender(candidate_data.get("genderid")),
        marital_status=get_marital_status(candidate_data.get("maritalstatusid")),
    )

    return candidate


def calculate_age(birth_date):
    return "Not provided"


def get_gender(gender_id):
    return "Male" if gender_id == 1 else "Female" if gender_id == 2 else "Other"


def get_marital_status(marital_status_id):
    return (
        "Single"
        if marital_status_id == 1
        else "Married"
        if marital_status_id == 2
        else "Other"
    )


if __name__ == "__main__":
    process_resumes()
