import glob
import json
import os
import time
import uuid
from datetime import datetime

from dotenv import load_dotenv
from elasticsearch import Elasticsearch
from openai import OpenAI

from screening_batch.helpers.get_job import fetch_job_information
from screening_batch.prompt import system_prompt, user_prompt
from screening_batch.utils import load_template, render_job_template
from services.redis.redis import rd

_ = load_dotenv()

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY_SCREENING"))

es = Elasticsearch(
    "{}:{}".format(os.getenv("ELASTICSEARCH_HOST"), os.getenv("ELASTICSEARCH_PORT")),
    basic_auth=(os.getenv("ELASTICSEARCH_USER"), os.getenv("ELASTICSEARCH_PASSWORD")),
)

BASE_PATH = os.path.dirname(os.path.abspath(__file__))

batch_files_folder = os.path.join("screening_batch", "batch-files")
file_content_folder = os.path.join("screening_batch", "contents")
results_folder = os.path.join("screening_batch", "results")

os.makedirs(batch_files_folder, exist_ok=True)
os.makedirs(file_content_folder, exist_ok=True)
os.makedirs(results_folder, exist_ok=True)

MODEL = "gpt-4o-mini"
TEMPERATURE = 0
# MAX_FILES = 10000


with open("screening_batch/schema.json", "r") as f:
    json_schema = json.loads(f.read())


def create_task(file):
    filename = os.path.basename(file)
    job_id, job_app_id, user_id, resume_id = os.path.splitext(filename)[0].split("_")
    content_file = os.path.join(
        file_content_folder, f"{job_id}_{job_app_id}_{user_id}_{resume_id}.txt"
    )
    job_info = fetch_job_information(job_id)
    template_path = os.path.join("screening_batch", "template", "job", "job.jinja")
    job_template_str = load_template(template_path)
    job_content = render_job_template(job_info, job_template_str)

    with open(content_file, "r") as f:
        candidate_content = f.read()

    messages = [
        {"role": "system", "content": system_prompt},
        {
            "role": "user",
            "content": user_prompt.format(
                schema=json_schema, candidate=candidate_content, job=job_content
            ),
        },
    ]

    task = {
        "custom_id": f"{job_id}_{job_app_id}_{user_id}_{resume_id}",
        "method": "POST",
        "url": "/v1/chat/completions",
        "body": {
            # This is what you would have in your Chat Completions API call
            "model": MODEL,
            "temperature": TEMPERATURE,
            "response_format": {"type": "json_object"},
            "messages": messages,
        },
    }
    return task


def create_batch_file(tasks):
    file_name = str(uuid.uuid4())
    with open(os.path.join(batch_files_folder, f"{file_name}.jsonl"), "w") as file:
        for obj in tasks:
            file.write(json.dumps(obj) + "\n")
    return file_name


def upload_batch_file(file_name):
    batch_file = client.files.create(
        file=open(os.path.join(batch_files_folder, f"{file_name}.jsonl"), "rb"),
        purpose="batch",
    )
    return batch_file


def create_batch_job(batch_file_id):
    batch_job = client.batches.create(
        input_file_id=batch_file_id,
        endpoint="/v1/chat/completions",
        completion_window="24h",
    )
    return batch_job


def create_batch_files(glob_path, max_files):
    list_batch_file = []
    files = glob.glob(glob_path)
    tasks = []
    total_files = 0
    for file in files:
        task = create_task(file)
        if task is None:
            continue
        tasks.append(task)
        total_files += 1
        if total_files == max_files:
            file_name = create_batch_file(tasks)
            batch_file = upload_batch_file(file_name)
            list_batch_file.append(batch_file)
            tasks = []
            total_files = 0
    if len(tasks) > 0:
        file_name = create_batch_file(tasks)
        batch_file = upload_batch_file(file_name)
        list_batch_file.append(batch_file)
    return list_batch_file


if __name__ == "__main__":
    redis_key = "screening_batch_processing"
    batch_job_ids = []
    glob_path = "screening_batch/contents/*.*"
    list_batch_file = create_batch_files(glob_path)
    for batch_file in list_batch_file:
        batch_file_id = batch_file.id
        batch_job = create_batch_job(batch_file_id)
        batch_job_ids.append(batch_job.id)
        rd.set(redis_key, json.dumps(batch_job_ids))
    print("Push Job Done")
