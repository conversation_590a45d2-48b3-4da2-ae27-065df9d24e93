weights = {
    "Skills": {"weight": 15, "required": True},
    "Education": {"weight": 5, "required": True},
    "Job title": {"weight": 40, "required": True},
    "Experience": {"weight": 15, "required": True},
}


BRIEF_EVALUATION_SELECTION = {
    "20": {"strength": 1, "weakness": 4},
    "40": {"strength": 2, "weakness": 3},
    "60": {"strength": 3, "weakness": 2},
    "80": {"strength": 4, "weakness": 1},
    "100": {"strength": 5, "weakness": 0},
}

UNMATCHED_LOCATION_WARNING = {
    "en": "This candidate may not match the job location.",
    "vi": "Ứng viên này có thể không phù hợp với địa điểm công việc.",
}

def postprocessing_response(response):
    try:
        result = {
            "brief_evaluation": response["brief_evaluation"],
            "detailed_evaluation": [],
        }

        matching_score = 0
        result["matching_score"] = matching_score
        total = 0

        for detailed_evaluation in response["detailed_evaluation"]:
            weight = (
                weights.get(
                    detailed_evaluation["criterion"], {"weight": 0, "required": False}
                )
                if any(weights.values())
                else {"weight": 100, "required": True}
            )

            matching_score += weight["weight"] * detailed_evaluation["score"] / 10
            total += (
                weight["weight"]
                if weight["required"]
                or (not weight["required"] and detailed_evaluation["score"] > 0)
                else 0
            )

            result["detailed_evaluation"].append(
                {
                    "criterion": detailed_evaluation["criterion"],
                    "score": detailed_evaluation["score"] * 10,
                    "reasoning": detailed_evaluation["reasoning"],
                }
            )
        if total > 0:
            result["matching_score"] = int(matching_score * 100 / total)

        location_matching = []
        try:
            if not response["location_matching"]["is_matching"] and (
                response["location_matching"]["candidate_home_city"]
                or len(response["location_matching"]["desired_job_locations"]) > 0
            ):
                location_matching = [UNMATCHED_LOCATION_WARNING]
        except:
            pass

        for key, val in BRIEF_EVALUATION_SELECTION.items():
            if result["matching_score"] > 80 and len(location_matching) > 0:
                result["brief_evaluation"] = {
                    "strengths": response["brief_evaluation"]["strengths"][:4],
                    "weaknesses": location_matching[:1],
                }
                break
            if result["matching_score"] <= int(key):
                result["brief_evaluation"] = {
                    "strengths": response["brief_evaluation"]["strengths"][
                        : val["strength"]
                    ],
                    "weaknesses": (
                        location_matching[:1]
                        + response["brief_evaluation"]["weaknesses"]
                    )[: val["weakness"]],
                }
                break

        return result
    except Exception as e:
        print(e)
        return response
