import json
import logging

from screening_batch.utils import translate_criterion_to_en, translate_criterion_to_vi
from services.log import log
from services.mysql.mysql import get_connection

logger = log.init_log("logs/log.log")

weights = {
    "Skills": {"weight": 15, "required": True},
    "Education": {"weight": 5, "required": True},
    "Job title": {"weight": 40, "required": True},
    "Experience": {"weight": 15, "required": True},
}


def insert_screening_results(response):
    connection = get_connection(write=True)
    try:
        with connection.cursor() as cursor:
            connection.begin()

            matching_score = response["result"]["matching_score"]
            job_app_id = response["jobAppId"]
            job_id = int(response["jobId"])

            update_or_insert_matching_score_query = """
            INSERT INTO tbljob_application_extra_info (jobAppId, matchingScore, privacyPolicyId, privacyPolicyVersionId) 
            VALUES (%s, %s, 0, 0) 
            ON DUPLICATE KEY UPDATE matchingScore = VALUES(matchingScore)
            """
            cursor.execute(update_or_insert_matching_score_query, (job_app_id, matching_score))

            delete_criteria_results_query = """
            DELETE FROM tbljob_application_screening_criteria
            WHERE jobAppId = %s
            """
            cursor.execute(delete_criteria_results_query, (job_app_id))

            delete_evaluation_query = """
            DELETE FROM tbljob_application_screening_evaluation
            WHERE jobAppId = %s
            """
            cursor.execute(delete_evaluation_query, (job_app_id))

            for evaluation in response["result"]["detailed_evaluation"]:
                criterion = evaluation["criterion"]
                en_criterion = translate_criterion_to_en(criterion)
                vi_criterion = translate_criterion_to_vi(criterion)
                weight = weights.get(en_criterion)["weight"]

                insert_criteria_query = """
                INSERT INTO tbljob_screening_criteria (jobId, criteriaName, criteriaWeight, languageCode)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                jobId = VALUES(jobId),
                criteriaName = VALUES(criteriaName),
                criteriaWeight = VALUES(criteriaWeight),
                languageCode = VALUES(languageCode)
                """

                cursor.execute(
                    insert_criteria_query, (job_id, vi_criterion, weight, "vi")
                )
                job_criteria_id = cursor.lastrowid

                if job_criteria_id == 0:
                    get_job_criteria_query = """
                    SELECT jobCriteriaId
                    FROM tbljob_screening_criteria
                    WHERE jobId = %s AND criteriaName = %s AND languageCode = %s
                    """
                    cursor.execute(get_job_criteria_query, (job_id, vi_criterion, "vi"))
                    result = cursor.fetchone()
                    job_criteria_id = result[0]

                score = evaluation["score"]
                reasoning_vi = evaluation["reasoning"]["vi"]
                insert_app_screening_criteria_query = """
                INSERT INTO tbljob_application_screening_criteria (jobAppId, jobCriteriaId, content, criteriaScore, languageCode, createdOn)
                VALUES (%s, %s, %s, %s, %s, NOW())
                """
                cursor.execute(
                    insert_app_screening_criteria_query,
                    (job_app_id, job_criteria_id, reasoning_vi, score, "vi"),
                )

                cursor.execute(
                    insert_criteria_query, (job_id, en_criterion, weight, "en")
                )
                job_criteria_id = cursor.lastrowid

                if job_criteria_id == 0:
                    get_job_criteria_query = """
                    SELECT jobCriteriaId
                    FROM tbljob_screening_criteria
                    WHERE jobId = %s AND criteriaName = %s AND languageCode = %s
                    """
                    cursor.execute(get_job_criteria_query, (job_id, en_criterion, "en"))
                    result = cursor.fetchone()
                    job_criteria_id = result[0]

                reasoning_en = evaluation["reasoning"]["en"]
                cursor.execute(
                    insert_app_screening_criteria_query,
                    (job_app_id, job_criteria_id, reasoning_en, score, "en"),
                )

            brief_strengths = response["result"]["brief_evaluation"]["strengths"]
            brief_weaknesses = response["result"]["brief_evaluation"]["weaknesses"]

            for strength in brief_strengths:
                insert_evaluation_query = """
                INSERT INTO tbljob_application_screening_evaluation (jobAppId, status, content, languageCode, createdOn)
                VALUES (%s, 1, %s, 'en', NOW()), (%s, 1, %s, 'vi', NOW())
                """
                cursor.execute(
                    insert_evaluation_query,
                    (job_app_id, strength["en"], job_app_id, strength["vi"]),
                )
            for weakness in brief_weaknesses:
                insert_evaluation_query = """
                INSERT INTO tbljob_application_screening_evaluation (jobAppId, status, content, languageCode, createdOn)
                VALUES (%s, 2, %s, 'en', NOW()), (%s, 2, %s, 'vi', NOW())
                """
                cursor.execute(
                    insert_evaluation_query,
                    (job_app_id, weakness["en"], job_app_id, weakness["vi"]),
                )

            connection.commit()
    except Exception as e:
        connection.rollback()
        logger.setLevel(logging.ERROR)
        logger.error(json.dumps(response))

    finally:
        connection.close()
