import json
import logging
import os
import time
from datetime import datetime

from dotenv import load_dotenv
from elasticsearch import Elasticsearch
from openai import OpenAI

from screening_batch.helpers.insert_screening_results import insert_screening_results
from screening_batch.helpers.postprocess_matching_score import postprocessing_response
from screening_batch.models.screening import ScreeningResults
from screening_batch.utils import get_elasticsearch_mapping
from services.log import log
from services.redis.redis import rd

_ = load_dotenv()

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY_SCREENING"))
es = Elasticsearch(
    f"{os.getenv('ELASTICSEARCH_HOST')}:{os.getenv('ELASTICSEARCH_PORT')}",
    basic_auth=(os.getenv("ELASTICSEARCH_USER"), os.getenv("ELASTICSEARCH_PASSWORD")),
)

results_folder = os.path.join("screening_batch", "results")
os.makedirs(results_folder, exist_ok=True)

logger = log.init_log("logs/log.log")
pricing_logger = log.init_log("screening_batch/logs/pricing.log")


def get_batch_job_results(batch_job_id):
    print(f"Checking...Batch job id {batch_job_id}")
    results = []
    batch_job_retrieve = client.batches.retrieve(batch_job_id)
    if batch_job_retrieve.status == "completed":
        print(f"Batch job id {batch_job_retrieve.id} is completed")
        result_file_id = (
            batch_job_retrieve.output_file_id or batch_job_retrieve.error_file_id
        )
        result = client.files.content(result_file_id).content
        result_path = os.path.join(results_folder, f"{batch_job_retrieve.id}.jsonl")
        with open(result_path, "wb") as file:
            file.write(result)

        with open(result_path, "r") as file:
            results.extend(json.loads(line.strip()) for line in file)
    else:
        print(f"Batch job id {batch_job_id} is not completed yet!")
        return None

    return results


def insert_to_es(batch_job_id, results):
    index = f"screening-batch-results_{datetime.now().strftime('%Y.%m.%d')}"
    mapping = get_elasticsearch_mapping(ScreeningResults)

    if not es.indices.exists(index=index):
        es.indices.create(index=index, body=mapping)

    batch_pricing = 0
    for result in results:
        job_id, job_app_id, user_id, resume_id = result["custom_id"].split("_")
        pricing = 0
        try:
            prompt_tokens = result["response"]["body"]["usage"]["prompt_tokens"]
            completion_tokens = result["response"]["body"]["usage"]["completion_tokens"]
            pricing += (
                prompt_tokens * 0.000075 / 1000 + completion_tokens * 0.000300 / 1000
            )
            batch_pricing += pricing
        except:
            pass
        status_code = result["response"]["status_code"]

        if status_code == 200:
            content = result["response"]["body"]["choices"][0]["message"]["content"]
            response = json.loads(content)
            try:
                response = ScreeningResults(**json.loads(content))
                response = response.dict(by_alias=True)
            except Exception as e:
                print(e)
                logger.setLevel(logging.ERROR)
                logger.error(
                    json.dumps(
                        {
                            "@timestamp": datetime.utcnow().strftime(
                                "%Y-%m-%dT%H:%M:%S.%fZ"
                            ),
                            "jobId": int(job_id),
                            "jobAppId": int(job_app_id),
                            "userId": int(user_id),
                            "resumeId": int(resume_id),
                            "result": response,
                        }
                    )
                )
            response = postprocessing_response(response)
            data = {
                "@timestamp": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "jobId": int(job_id),
                "jobAppId": int(job_app_id),
                "userId": int(user_id),
                "resumeId": int(resume_id),
                "result": response,
                "pricing": pricing,
            }
            es.index(index=index, document=data)
            insert_screening_results(data)

    print(f"BATCH {batch_job_id} PRICING: {batch_pricing}")
    pricing_logger.setLevel(logging.INFO)
    pricing_logger.info("BATCH %s PRICING: %s", batch_job_id, batch_pricing)


if __name__ == "__main__":
    redis_key = "screening_batch_processing"
    batch_job_ids = json.loads(rd.get(redis_key) or "[]")
    while batch_job_ids:
        for batch_job_id in batch_job_ids[:]:
            results = get_batch_job_results(batch_job_id)
            if results:
                insert_to_es(batch_job_id, results)
                batch_job_ids.remove(batch_job_id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        print("Number of remaining batches:", len(batch_job_ids))
        time.sleep(15)
    print("Get Results Done")
