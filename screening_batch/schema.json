{"$defs": {"LocationMatching": {"properties": {"candidate_home_city": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "The city where the candidate resides. Return None if not specified.", "title": "Candidate Home City"}, "desired_job_locations": {"description": "A list of cities where the candidate is seeking job opportunities. Return an empty list if not specified.", "items": {"type": "string"}, "title": "Desired Job Locations", "type": "array"}, "job_locations": {"description": "A list of cities where the job opportunities are available. Return an empty list if not specified.", "items": {"type": "string"}, "title": "Job Locations", "type": "array"}, "is_matching": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "\n        Evaluate if the candidate's location preferences align with the job's working locations, following these guidelines:\n        Return true if the candidate's home city or any of their desired job locations matches or is included in the job's listed cities. <PERSON><PERSON> return false.\n        ", "title": "Is Matching"}}, "required": ["desired_job_locations", "job_locations"], "title": "LocationMatching", "type": "object"}, "MultilingualReasoning": {"properties": {"strengths": {"description": "A list of up to 5 candidate strengths, each in both Vietnamese and English.", "items": {"$ref": "#/$defs/MultilingualText"}, "title": "Strengths", "type": "array"}, "weaknesses": {"description": "A list of up to 5 candidate weaknesses, each in both Vietnamese and English.", "items": {"$ref": "#/$defs/MultilingualText"}, "title": "Weaknesses", "type": "array"}}, "title": "MultilingualReasoning", "type": "object"}, "MultilingualText": {"properties": {"vi": {"description": "Vietnamese text, at least 80 characters long", "title": "Vi", "type": "string"}, "en": {"description": "English text, at least 80 characters long", "title": "En", "type": "string"}}, "required": ["vi", "en"], "title": "MultilingualText", "type": "object"}, "ScreeningCriterionScoreV1": {"properties": {"criterion": {"enum": ["Skills", "Experience", "Job title", "Education"], "title": "Criterion", "type": "string"}, "score": {"title": "Score", "type": "integer"}, "reasoning": {"allOf": [{"$ref": "#/$defs/MultilingualText"}], "description": "provide a detailed evaluation, at least 400 characters long, for the specific criterion, presented in telegraphic style. Omit any scoring calculations in your analysis, focusing purely on qualitative assessment."}}, "required": ["criterion", "score", "reasoning"], "title": "ScreeningCriterionScoreV1", "type": "object"}}, "properties": {"job_description_level": {"anyOf": [{"enum": ["Director and above", "Manager", "Experienced (non-manager)", "Fresher/Entry level", "Intern/Student"], "type": "string"}, {"type": "string"}], "description": "Analyze the job description and select a job level from the list", "title": "Job Description Level"}, "candidate_job_level": {"anyOf": [{"enum": ["Director and above", "Manager", "Experienced (non-manager)", "Fresher/Entry level", "Intern/Student"], "type": "string"}, {"type": "string"}], "description": "Analyze the candidate profile and select a job level from the list", "title": "Candidate Job Level"}, "job_level_matching": {"description": "Compare the job levels of the candidate and the job description to identify if this candidate is qualified, underqualified, or overqualified for the job", "enum": ["qualified", "overqualified", "underqualified"], "title": "Job Level Matching", "type": "string"}, "candidate_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "name of the candidate", "title": "Candidate Name"}, "candidate_job_title": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "job title of the candidate", "title": "Candidate Job Title"}, "candidate_latest_company": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "latest company of the candidate", "title": "Candidate Latest Company"}, "brief_evaluation": {"allOf": [{"$ref": "#/$defs/MultilingualReasoning"}], "description": "Provide a list of concise evaluations, divided into strengths and weaknesses, for each criterion. Use a telegraphic style with implied subjects, avoiding direct references such as 'candidate' or personal pronouns."}, "detailed_evaluation": {"items": {"$ref": "#/$defs/ScreeningCriterionScoreV1"}, "title": "Detailed Evaluation", "type": "array"}, "location_matching": {"$ref": "#/$defs/LocationMatching"}}, "required": ["job_description_level", "candidate_job_level", "job_level_matching", "candidate_name", "candidate_job_title", "candidate_latest_company", "brief_evaluation", "detailed_evaluation", "location_matching"]}