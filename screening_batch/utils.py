import os
import subprocess
import sys
import time
from base64 import b64encode
from typing import Any, Dict

import pymysql
import requests
import tiktoken
from dotenv import load_dotenv
from jinja2 import Template
from pydantic import BaseModel

from screening_batch.models.candidate import CandidateData
from screening_batch.models.job import Job

_ = load_dotenv()


def connect_mysql_db(host, port, user, password, db):
    return pymysql.connect(
        host=host,
        port=port,
        user=user,
        passwd=password,
        db=db,
        charset="utf8",
    )


def update_matching_score(job_app_id, matching_score):
    try:
        query = f"""
INSERT INTO `tbljob_application_extra_info` (`jobAppId`, `matchingScore`)
VALUES ({job_app_id}, {matching_score}) 
ON DUPLICATE KEY UPDATE
`matchingScore` = {matching_score}
"""
        session = connect_mysql_db(
            os.getenv("MYSQL_HOST"),
            int(os.getenv("MYSQL_PORT")),
            os.getenv("MYSQL_USER"),
            os.getenv("MYSQL_PASSWORD"),
            os.getenv("MYSQL_DATABASE"),
        )
        cursor = session.cursor()
        cursor.execute(query)
        session.commit()
        cursor.close()
        session.close()
    except Exception as e:
        print("Error: ", e)


def check_matching_score_exist(job_app_id):
    try:
        query = """
SELECT count(1) FROM {} WHERE jobAppId = {} AND matchingScore is NULL;
""".format(
            "tbljob_application_extra_info", job_app_id
        )
        session = connect_mysql_db(
            os.getenv("MYSQL_HOST"),
            int(os.getenv("MYSQL_PORT")),
            os.getenv("MYSQL_USER"),
            os.getenv("MYSQL_PASSWORD"),
            os.getenv("MYSQL_DATABASE"),
        )
        cursor = session.cursor()
        cursor.execute(query)
        rows = cursor.fetchall()
        cursor.close()
        session.close()
    except Exception as e:
        print("Error: ", e)
    if rows[0][0] == 0:
        return False
    return False


def get_elasticsearch_mapping(pydantic_model: BaseModel) -> Dict[str, Any]:
    # Define a mapping between Pydantic types and Elasticsearch types
    type_mapping = {
        int: "integer",
        str: "text",
        float: "float",
        bool: "boolean",
        list: "nested",
        dict: "object",
    }

    # Function to get Elasticsearch type from Python type
    def get_es_type(py_type):
        return type_mapping.get(py_type, "text")

    properties = {}
    for field_name, field in pydantic_model.__annotations__.items():
        if hasattr(field, "__origin__"):
            if field.__origin__ == list:
                properties[field_name] = {
                    "type": "nested",
                    "properties": {"value": {"type": get_es_type(field.__args__[0])}},
                }
            elif field.__origin__ == dict:
                properties[field_name] = {"type": "object"}
        else:
            properties[field_name] = {"type": get_es_type(field)}

    return {"mappings": {"properties": properties}}


def num_tokens_from_messages(messages, model="gpt-3.5-turbo-0613"):
    """Return the number of tokens used by a list of messages."""
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        print("Warning: model not found. Using cl100k_base encoding.")
        encoding = tiktoken.get_encoding("cl100k_base")
    if model in {
        "gpt-3.5-turbo-0613",
        "gpt-3.5-turbo-16k-0613",
        "gpt-4-0314",
        "gpt-4-32k-0314",
        "gpt-4-0613",
        "gpt-4-32k-0613",
    }:
        tokens_per_message = 3
        tokens_per_name = 1
    elif model == "gpt-3.5-turbo-0301":
        tokens_per_message = (
            4  # every message follows <|start|>{role/name}\n{content}<|end|>\n
        )
        tokens_per_name = -1  # if there's a name, the role is omitted
    elif "gpt-3.5-turbo" in model:
        print(
            "Warning: gpt-3.5-turbo may update over time. Returning num tokens assuming gpt-3.5-turbo-0613."
        )
        return num_tokens_from_messages(messages, model="gpt-3.5-turbo-0613")
    elif "gpt-4" in model:
        print(
            "Warning: gpt-4 may update over time. Returning num tokens assuming gpt-4-0613."
        )
        return num_tokens_from_messages(messages, model="gpt-4-0613")
    else:
        raise NotImplementedError(
            f"""num_tokens_from_messages() is not implemented for model {model}. See https://github.com/openai/openai-python/blob/main/chatml.md for information on how messages are converted to tokens."""
        )
    num_tokens = 0
    for message in messages:
        num_tokens += tokens_per_message
        for key, value in message.items():
            num_tokens += len(encoding.encode(value))
            if key == "name":
                num_tokens += tokens_per_name
    num_tokens += 3  # every reply is primed with <|start|>assistant<|message|>
    return num_tokens


def convert_to_pdf(filename):
    filename_pdf = os.path.splitext(os.path.basename(filename))[0] + ".pdf"
    ans = subprocess.call(
        [
            "unoconvert",
            "--host",
            "***********",
            "--port",
            "2003",
            "--convert-to",
            "pdf",
            filename,
            filename_pdf,
        ],
    )
    if ans == 0:
        return filename_pdf
    else:
        return ""


def get_content(filename):
    try:
        url = os.getenv("TEXTRACT_API")
        with open(filename, "rb") as open_file:
            byte_content = open_file.read()
            base64_bytes = b64encode(byte_content)
            base64_string = base64_bytes.decode("utf-8")
            raw_data = {
                "data": base64_string,
                "file_type": filename.split(".")[-1],
            }
        response = requests.post(url, json=raw_data, timeout=15)
        if response.status_code == 200:
            return response.json()["text"]
    except:
        return ""


def translate_criterion_to_vi(criterion_name):
    criteria_name_dict = {
        "Skills": "Kỹ năng",
        "Experience": "Kinh nghiệm",
        "Job title": "Chức danh",
        "Education": "Học vấn",
    }

    return criteria_name_dict.get(criterion_name, criterion_name)


def translate_criterion_to_en(criterion_name):
    criteria_name_dict = {
        "Kỹ năng": "Skills",
        "Kinh nghiệm": "Experience",
        "Chức danh": "Job title",
        "Học vấn": "Education",
    }

    return criteria_name_dict.get(criterion_name, criterion_name)


def load_template(template_path: str) -> str:
    with open(template_path, "r") as file:
        return file.read()


def render_job_template(job_info: Job, template_str: str) -> str:
    template = Template(template_str)
    return template.render(job_data=job_info)


def render_candidate_template(candidate_info: CandidateData, template_str: str) -> str:
    template = Template(template_str)
    return template.render(candidate_data=candidate_info)
