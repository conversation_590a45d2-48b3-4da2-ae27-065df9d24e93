import datetime
import os

import numpy as np
import pandas as pd
import streamlit as st
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()


@st.fragment(run_every=30)
def dashboard():
    project = st.session_state.get("project", "AI_CV")
    limit = int(st.session_state.get("limit", 50))
    if project == "AI CV":
        client = OpenAI()
    else:
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY_SCREENING"))
    batches = client.batches.list(limit=limit).data
    if batches:
        result = []
        for batch in batches:
            data = [
                batch.id,
                batch.request_counts.completed,
                batch.request_counts.failed,
                batch.request_counts.total,
                batch.status,
                datetime.datetime.fromtimestamp(batch.created_at).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            ]
            result.append(data)
        df = pd.DataFrame(
            np.array(result),
            columns=[
                "Batch ID",
                "Completed",
                "Failed",
                "Total",
                "Status",
                "Created At",
            ],
        )
        st.table(df)


def setup_dashboard():
    st.set_page_config(layout="wide")
    st.title("AI Batch Dashboard")
    st.radio(
        "Project",
        options=["AI CV", "AI Screening"],
        index=0,
        key="project",
    )
    st.number_input(
        "Limit",
        min_value=1,
        max_value=100,
        value=st.session_state.get("limit", 50),
        step=10,
        key="limit",
    )
    dashboard()


if __name__ == "__main__":
    setup_dashboard()
