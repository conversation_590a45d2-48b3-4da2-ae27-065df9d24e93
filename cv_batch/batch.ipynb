{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "from datetime import datetime\n", "\n", "from dotenv import load_dotenv\n", "from elasticsearch import Elasticsearch\n", "from model import Resume\n", "from openai import OpenAI\n", "\n", "load_dotenv()\n", "\n", "client = OpenAI()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["batch_id: batch_kaoTRomUcAh0EQE5h1pvQZpE - status: in_progress - created_at 2024-07-29 07:21:04\n", "batch_id: batch_pmLCuqcNM0FdTXXQXjLu24fb - status: completed - created_at 2024-07-29 07:03:55\n", "batch_id: batch_4yYD4YC78aKJldpsAajTHrwv - status: completed - created_at 2024-07-29 06:51:27\n", "batch_id: batch_mGpzdOZqMB5Z7yhFHoZd5Kms - status: completed - created_at 2024-07-22 08:30:53\n", "batch_id: batch_x1gzyZYthf73LLHvMFkV65y2 - status: completed - created_at 2024-07-22 08:27:13\n", "batch_id: batch_WaiLhPzqNo9TrIL16AXnZ3GE - status: completed - created_at 2024-07-22 08:25:22\n", "batch_id: batch_C841ROWmxAazQdqUlvmQUEIy - status: completed - created_at 2024-07-22 08:23:28\n", "batch_id: batch_xz3OtowRADCKz8nrvCjby8bR - status: completed - created_at 2024-07-22 08:19:45\n", "batch_id: batch_fVjTozyWS5CTDuhIFaDiV9ib - status: completed - created_at 2024-07-22 08:17:54\n", "batch_id: batch_W15yIQpa4OEllPFOzMjkOTfL - status: completed - created_at 2024-07-22 08:13:57\n", "batch_id: batch_J8HssQBmvvpptOcck65EsYfu - status: completed - created_at 2024-07-22 08:11:20\n", "batch_id: batch_i4DFKe9BthS4EE1hJxTSLVQo - status: completed - created_at 2024-07-22 07:58:30\n", "batch_id: batch_lDV4TQdvdlPx1wL9LGdJe6Me - status: completed - created_at 2024-07-22 07:55:21\n", "batch_id: batch_hAFaAleEPuwj77IqSA9Cm66Z - status: completed - created_at 2024-07-22 07:52:12\n", "batch_id: batch_cSPWiOVIgsWLA814zc5JWlSq - status: completed - created_at 2024-07-22 07:49:33\n", "batch_id: batch_CGyJDwki8GuYPnOcA0gCUAcA - status: completed - created_at 2024-07-22 07:45:07\n", "batch_id: batch_Yuf3ZOxSB09XCJCXtBX1vvMF - status: completed - created_at 2024-07-22 07:41:27\n", "batch_id: batch_mlPSLqhwdvtV5rO9OAk2xnKx - status: completed - created_at 2024-07-22 07:31:05\n", "batch_id: batch_rXbo7gHeJ1FNX6koG8s7mX8T - status: completed - created_at 2024-07-22 07:24:18\n", "batch_id: batch_JHm7KP9K7BzTPoTXtAbth9gY - status: completed - created_at 2024-07-22 06:00:08\n", "batch_id: batch_STnAeui1DHErdKCsfmhJvLem - status: completed - created_at 2024-07-22 05:58:33\n", "batch_id: batch_6oqdYQtS5ABeBEcVOlcMy8cs - status: completed - created_at 2024-07-22 05:56:42\n", "batch_id: batch_Id9DjlHXh1qfCthEXWxAeEKy - status: completed - created_at 2024-07-22 05:26:12\n", "batch_id: batch_54UtIltsXOuoTilEhDZTA2b2 - status: completed - created_at 2024-07-22 04:40:35\n", "batch_id: batch_52y0Rfrx7Zbv57rGcckNlTTv - status: completed - created_at 2024-07-22 04:39:01\n", "batch_id: batch_wM4ZEU7hkZJ0cg7gZYypSrRU - status: completed - created_at 2024-07-22 04:37:41\n", "batch_id: batch_1UkEWQQUEjsV6CRrzytqxjSU - status: failed - created_at 2024-07-22 04:35:39\n", "batch_id: batch_6GUwJd813omF1lDzvAG1XHOS - status: completed - created_at 2024-07-22 04:33:32\n", "batch_id: batch_IwqLVZywRAiQI0e5qjYX5jDi - status: completed - created_at 2024-07-22 04:31:57\n", "batch_id: batch_CtjUGOATALjYc5ZJ7NlNzTJa - status: failed - created_at 2024-07-22 04:29:58\n", "batch_id: batch_0WHnbswRN6xP84NECPUMaPmn - status: completed - created_at 2024-07-22 04:27:17\n", "batch_id: batch_bMaVFi0VkD3bcABfp1dWmslt - status: completed - created_at 2024-07-22 04:24:40\n", "batch_id: batch_8Zjm3EwVEnPQ7SiAlyuUMP3A - status: failed - created_at 2024-07-22 04:12:39\n", "batch_id: batch_fCMuhdfc1bDIK5KeDkSKoXzf - status: completed - created_at 2024-07-22 04:09:30\n", "batch_id: batch_pvHk4MjZvZEmPdDSC6bY8lob - status: completed - created_at 2024-07-22 04:06:52\n", "batch_id: batch_1zVsOtLeEqvUtedT4kXQayay - status: completed - created_at 2024-06-27 16:10:12\n", "batch_id: batch_OjFu6TA4GMuwRttVwdjbCxr1 - status: completed - created_at 2024-06-27 16:06:22\n", "batch_id: batch_qGi3FDRJKW4sCWrI2fFRmKBH - status: completed - created_at 2024-06-27 16:03:01\n", "batch_id: batch_zEg4bzEPbWcBYmIJkUOmjAor - status: completed - created_at 2024-06-27 15:58:37\n", "batch_id: batch_7J1tO5dReXvOxcnxmX0qIHjX - status: completed - created_at 2024-06-27 15:21:25\n", "batch_id: batch_9Tb85FY5Cuo3rkfxDjOjir6S - status: completed - created_at 2024-06-27 15:19:08\n", "batch_id: batch_qwPWLdT1VA6c4pK8RbZQBZja - status: completed - created_at 2024-06-27 15:12:49\n", "batch_id: batch_QxsFT9u5Qu1LHfUrHM5JJfxP - status: completed - created_at 2024-06-27 15:08:10\n", "batch_id: batch_cOLA2pjJh7k4r9XY6fFhxZfT - status: completed - created_at 2024-06-27 10:36:25\n", "batch_id: batch_n102iXmbYSjqycYKgiR6eFti - status: completed - created_at 2024-06-27 10:31:54\n", "batch_id: batch_154ciEbAoBKxVlFj3sb2Qq8A - status: completed - created_at 2024-06-27 10:01:59\n", "batch_id: batch_NXPhrjL4KM9yChWHzRc0s1Wo - status: completed - created_at 2024-06-27 08:40:13\n", "batch_id: batch_MsVWynFymcJkGKdMpoBmnCPP - status: failed - created_at 2024-06-27 08:32:08\n", "batch_id: batch_qgs9E1LYQ1pinLDFSKaMGch6 - status: completed - created_at 2024-06-27 08:22:58\n", "batch_id: batch_9f08bHejJdePuV195Xs2xgGD - status: completed - created_at 2024-06-27 07:48:20\n", "batch_id: batch_mbNAL4dSfwon8DV0MTemoKqJ - status: completed - created_at 2024-06-27 07:41:25\n", "batch_id: batch_vlRtxMZRraZglbI61jePu1eZ - status: completed - created_at 2024-06-27 07:39:58\n", "batch_id: batch_hLCGa5UgmlW7cHuzJULY70Gt - status: failed - created_at 2024-06-27 07:20:59\n", "batch_id: batch_QqHj3KztgsRxeuy6Ow5zmSpS - status: failed - created_at 2024-06-27 07:20:56\n", "batch_id: batch_znD2EB97EY5fRYB55Xv9nlu6 - status: failed - created_at 2024-06-27 07:20:30\n", "batch_id: batch_invBvWc3hSaVUTH99l8a8oB0 - status: completed - created_at 2024-06-27 07:20:24\n", "batch_id: batch_Yam9YR6El2nlPXDzIj9rXRdc - status: failed - created_at 2024-06-27 07:19:03\n", "batch_id: batch_MNl9XhUUT3Tc9fncYhrhskF7 - status: failed - created_at 2024-06-27 07:18:59\n", "batch_id: batch_QmULVjVlAaqR0GXatRtWzrcg - status: failed - created_at 2024-06-27 07:18:32\n", "batch_id: batch_FRPqiUbSE8ThIB81TuuOjaDN - status: completed - created_at 2024-06-27 07:18:26\n", "batch_id: batch_BigTdIK8WOox6Qjp1tHieCQq - status: failed - created_at 2024-06-27 07:07:21\n", "batch_id: batch_qVbWbakmBC4tTCWIx7CTT6P9 - status: failed - created_at 2024-06-27 07:07:18\n", "batch_id: batch_PT1yAQJBPcSipoKvPwXeGXfy - status: failed - created_at 2024-06-27 07:06:55\n", "batch_id: batch_JLxKF0sVXdudCbwT5uurHcNJ - status: completed - created_at 2024-06-27 07:06:49\n", "batch_id: batch_pVDw4mqO559MFIJ5tLrf2ZZ6 - status: completed - created_at 2024-06-27 03:27:57\n", "batch_id: batch_PeDG81cO5LsHkqOP6nhUk8AL - status: failed - created_at 2024-06-26 11:38:29\n", "batch_id: batch_niU3wPPFzVryvT4VlbOYMxjv - status: failed - created_at 2024-06-26 11:38:04\n", "batch_id: batch_88DLGcmeV9ZQGX3qaGB3gzg0 - status: completed - created_at 2024-06-26 11:28:53\n", "batch_id: batch_HGcTDsiPXPVCrI1VXiynGGPe - status: failed - created_at 2024-06-26 11:26:22\n", "batch_id: batch_Tih3lmYdRFnXxrgO80HeRIdD - status: completed - created_at 2024-06-25 12:32:17\n", "batch_id: batch_tMAEXYxctpOJfnVBA1LiESqZ - status: completed - created_at 2024-06-25 11:30:15\n", "batch_id: batch_qrh3UVfkEqycqVkFbZHH1TTl - status: completed - created_at 2024-06-25 11:20:01\n", "batch_id: batch_od5gfVnEIvsMxmb9pA9uQRtj - status: completed - created_at 2024-06-25 11:11:59\n", "batch_id: batch_3xPQMDyEKhmfpDRhyWLZP8me - status: completed - created_at 2024-06-25 11:02:09\n", "batch_id: batch_XYjR2m9FJkwrVBgDEkqbgJoI - status: completed - created_at 2024-06-25 10:46:42\n", "batch_id: batch_r4lm3C5iMSHfuKgwb4oOghM4 - status: cancelled - created_at 2024-06-25 10:44:28\n", "batch_id: batch_VJNwGmcuUp59L1IOG4vd1m9D - status: completed - created_at 2024-06-25 09:09:28\n", "batch_id: batch_lgqHf8zoahGQzkAHOgMYrt3y - status: completed - created_at 2024-06-25 08:57:46\n", "batch_id: batch_T4zejK82ZUAW82Kj7NTZUyN8 - status: completed - created_at 2024-06-25 08:46:31\n", "batch_id: batch_jfY1MMCduk0ocDuzv5Lg9VH4 - status: completed - created_at 2024-06-25 08:34:55\n", "batch_id: batch_DhTpAGzZQ0ohKaWn6UiWF9Ei - status: completed - created_at 2024-06-25 08:28:12\n", "batch_id: batch_WFKe9TT7wqKjIMlMJkB2iqXx - status: completed - created_at 2024-06-25 08:22:20\n", "batch_id: batch_A4GRek3ZiX1SyD71wl3hYDu2 - status: completed - created_at 2024-06-24 04:24:37\n", "batch_id: batch_rfIuC7cRwIVj6e0GyTl1gndX - status: completed - created_at 2024-06-24 04:16:50\n", "batch_id: batch_XI4O9Dxyd20TFMpJ2qYiQy6X - status: completed - created_at 2024-06-24 03:51:02\n", "batch_id: batch_UOy54NzYW3jZ25ilgfgrgMRD - status: completed - created_at 2024-06-21 07:03:07\n", "batch_id: batch_ObqVkpgCrXbD4aqaaBbWnnvd - status: completed - created_at 2024-06-21 06:58:02\n", "batch_id: batch_wcWFeRJnX9lLiNlbVFfDl87X - status: completed - created_at 2024-06-21 03:55:53\n", "batch_id: batch_Ms3l92Mj5m1P9PYUou06Myif - status: completed - created_at 2024-06-20 16:56:59\n", "batch_id: batch_AmqJzoCSSpOMM31068qMOQHH - status: cancelled - created_at 2024-06-20 16:49:57\n", "batch_id: batch_QlBB5qhpI8CQ5jzyhPE1IjZC - status: completed - created_at 2024-06-20 08:11:57\n", "batch_id: batch_TEuKgKR8jL51oafLCzgZXOmp - status: completed - created_at 2024-06-19 11:53:38\n", "batch_id: batch_SXIFYcxkI2wLVA2onFuWBr9O - status: completed - created_at 2024-06-19 11:13:57\n", "batch_id: batch_WFRN6rsFpVjXDVh0wr1zw55I - status: completed - created_at 2024-06-19 11:13:50\n"]}], "source": ["for batch in client.batches.list(limit=10):\n", "    print(\n", "        \"batch_id:\",\n", "        batch.id,\n", "        \"-\",\n", "        \"status:\",\n", "        batch.status,\n", "        \"-\",\n", "        \"created_at\",\n", "        datetime.utcfromtimestamp(batch.created_at).strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    )"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "\n", "def get_batch_job_results(result_file_name):\n", "    results = []\n", "    with open(result_file_name, \"r\") as file:\n", "        for line in file:\n", "            # Parsing the JSON string into a dict and appending to the list of results\n", "            json_object = json.loads(line.strip())\n", "            results.append(json_object)\n", "    return results\n", "\n", "\n", "for result_file in glob.glob(\"results/*.jsonl\"):\n", "    results = get_batch_job_results(result_file)\n", "\n", "    for result in results:\n", "        content = result[\"response\"][\"body\"][\"choices\"][0][\"message\"][\"content\"]\n", "        try:\n", "            output = json.loads(content)\n", "            output = Resume(**output).dict(by_alias=True)\n", "            with open(\"results_resume/{}.json\".format(result[\"custom_id\"]), \"w\") as f:\n", "                f.write(json.dumps(output, indent=2, ensure_ascii=False))\n", "        except Exception as e:\n", "            print(\"Error:\", e)\n", "            continue"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "AuthenticationException", "evalue": "AuthenticationException(401, '')", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;**************************\u001b[0m                   Traceback (most recent call last)", "Cell \u001b[0;32mIn[8], line 13\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmapping.json\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m     11\u001b[0m     mapping \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(f\u001b[38;5;241m.\u001b[39mread())\n\u001b[0;32m---> 13\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mes\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mindices\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexists\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindex\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m     14\u001b[0m     es\u001b[38;5;241m.\u001b[39mindices\u001b[38;5;241m.\u001b[39mcreate(index\u001b[38;5;241m=\u001b[39mindex, body\u001b[38;5;241m=\u001b[39mmapping)\n", "File \u001b[0;32m~/.pyenv/versions/3.11.6/envs/dev-3.11/lib/python3.11/site-packages/elasticsearch/client/utils.py:92\u001b[0m, in \u001b[0;36mquery_params.<locals>._wrapper.<locals>._wrapped\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     90\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m kwargs:\n\u001b[1;32m     91\u001b[0m         params[p] \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(p)\n\u001b[0;32m---> 92\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.11.6/envs/dev-3.11/lib/python3.11/site-packages/elasticsearch/client/indices.py:304\u001b[0m, in \u001b[0;36mIndicesClient.exists\u001b[0;34m(self, index, params, headers)\u001b[0m\n\u001b[1;32m    301\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m index \u001b[38;5;129;01min\u001b[39;00m SKIP_IN_PATH:\n\u001b[1;32m    302\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEmpty value passed for a required argument \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mindex\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 304\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtransport\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mperform_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    305\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mHEAD\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_make_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindex\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\n\u001b[1;32m    306\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.11.6/envs/dev-3.11/lib/python3.11/site-packages/elasticsearch/transport.py:355\u001b[0m, in \u001b[0;36mTransport.perform_request\u001b[0;34m(self, method, url, headers, params, body)\u001b[0m\n\u001b[1;32m    352\u001b[0m connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_connection()\n\u001b[1;32m    354\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 355\u001b[0m     status, headers_response, data \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mperform_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    356\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    357\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    358\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    359\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    360\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    361\u001b[0m \u001b[43m        \u001b[49m\u001b[43mignore\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mignore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    362\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    363\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    365\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m TransportError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    366\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m method \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHEAD\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m e\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m404\u001b[39m:\n", "File \u001b[0;32m~/.pyenv/versions/3.11.6/envs/dev-3.11/lib/python3.11/site-packages/elasticsearch/connection/http_urllib3.py:248\u001b[0m, in \u001b[0;36mUrllib3HttpConnection.perform_request\u001b[0;34m(self, method, url, params, body, timeout, ignore, headers)\u001b[0m\n\u001b[1;32m    244\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;241m200\u001b[39m \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mstatus \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m300\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ignore:\n\u001b[1;32m    245\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlog_request_fail(\n\u001b[1;32m    246\u001b[0m         method, full_url, url, orig_body, duration, response\u001b[38;5;241m.\u001b[39mstatus, raw_data\n\u001b[1;32m    247\u001b[0m     )\n\u001b[0;32m--> 248\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstatus\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mraw_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    250\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlog_request_success(\n\u001b[1;32m    251\u001b[0m     method, full_url, url, orig_body, response\u001b[38;5;241m.\u001b[39mstatus, raw_data, duration\n\u001b[1;32m    252\u001b[0m )\n\u001b[1;32m    254\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus, response\u001b[38;5;241m.\u001b[39mgetheaders(), raw_data\n", "File \u001b[0;32m~/.pyenv/versions/3.11.6/envs/dev-3.11/lib/python3.11/site-packages/elasticsearch/connection/base.py:243\u001b[0m, in \u001b[0;36mConnection._raise_error\u001b[0;34m(self, status_code, raw_data)\u001b[0m\n\u001b[1;32m    240\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mValueError\u001b[39;00m, \u001b[38;5;167;01mTypeError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    241\u001b[0m     logger\u001b[38;5;241m.\u001b[39mwarning(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUndecodable raw error response from server: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, err)\n\u001b[0;32m--> 243\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m HTTP_EXCEPTIONS\u001b[38;5;241m.\u001b[39mget(status_code, TransportError)(\n\u001b[1;32m    244\u001b[0m     status_code, error_message, additional_info\n\u001b[1;32m    245\u001b[0m )\n", "\u001b[0;**************************\u001b[0m: AuthenticationException(401, '')"]}], "source": ["es = Elasticsearch(\n", "    \"{}:{}\".format(os.getenv(\"ELASTICSEARCH_HOST\"), os.getenv(\"ELASTICSEARCH_PORT\")),\n", "    basic_auth=(os.getenv(\"ELASTICSEARCH_USER\"), os.getenv(\"ELASTICSEARCH_PASSWORD\")),\n", ")\n", "\n", "index = \"{}_{}\".format(os.getenv(\"ELASTICSEARCH_INDEX\"), datetime.now().strftime(\"%Y.%m.%d\"))\n", "\n", "with open(\"mapping.json\", \"r\") as f:\n", "    mapping = json.loads(f.read())\n", "\n", "if not es.indices.exists(index=index):\n", "    es.indices.create(index=index, body=mapping)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "from utils import check_resume_exists, update_status_resume\n", "\n", "results_resume_folder = \"results_resume/*.json\"\n", "\n", "\n", "for file in glob.glob(results_resume_folder):\n", "    user_id, resume_id = os.path.splitext(os.path.basename(file))[0].split(\"_\")\n", "    now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "    with open(file, \"r\") as f:\n", "        result = json.loads(f.read())\n", "        result = Resume(**result).dict(by_alias=True)\n", "    data = {\n", "        \"@timestamp\": datetime.utcnow().strftime(\"%Y-%m-%dT%H:%M:%S.%fZ\"),\n", "        \"userId\": int(user_id),\n", "        \"resumeId\": int(resume_id),\n", "        \"result\": result,\n", "    }\n", "    if not check_resume_exists(resume_id):\n", "        parsingStatus = 0\n", "        description = \"\"\n", "        try:\n", "            es.index(\n", "                index=index,\n", "                document=data,\n", "            )\n", "            parsingStatus = 1\n", "        except Exception as e:\n", "            print(\"Error:\", e)\n", "            description = \"Error\"\n", "            parsingStatus = 2\n", "        update_status_resume(\n", "            user_id=user_id,\n", "            resume_id=resume_id,\n", "            parsingStatus=parsingStatus,\n", "            description=description,\n", "            created_on=now,\n", "            updated_on=now,\n", "        )\n", "        print(\"OK!!!\")\n", "    else:\n", "        print(\"resume id {} is exists\".format(resume_id))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dev-3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 2}