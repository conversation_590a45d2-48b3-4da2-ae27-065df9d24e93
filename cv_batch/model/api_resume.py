from pydantic import BaseModel, Field, HttpUrl, EmailStr, ConfigDict, validator
from typing import List, Optional
from datetime import datetime


class ContactInfo(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    job_title: Optional[str] = None
    birthday: Optional[str] = None
    gender: Optional[int] = None
    marriage_status: Optional[int] = None
    address_city: Optional[int] = None
    address_district: Optional[str] = None
    address: Optional[str] = None
    linkedin: Optional[str] = None
    @validator('email', pre=True, always=True)
    def validate_email(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('gender', pre=True, always=True)
    def validate_gender(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('marriage_status', pre=True, always=True)
    def validate_marriage_status(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('address_city', pre=True, always=True)
    def validate_address_city(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('linkedin', pre=True, always=True)
    def validate_linkedin(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class BasicInfo(BaseModel):
    highest_education: Optional[int] = 11
    current_job_level: Optional[int] = None
    current_industry: Optional[int] = None
    current_job_function: Optional[int] = None
    years_of_experience: Optional[int] = None
    @validator('highest_education', pre=True, always=True)
    def validate_highest_education(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('current_job_level', pre=True, always=True)
    def validate_current_job_level(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('current_job_function', pre=True, always=True)
    def validate_current_job_function(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('years_of_experience', pre=True, always=True)
    def validate_years_of_experience(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class Summary(BaseModel):
    objectives: Optional[str] = None


class WorkingPreference(BaseModel):
    expected_salary: Optional[int] = None
    unit: Optional[str] = None
    expected_job_function: Optional[int]
    expected_industry: Optional[int] = None
    expected_job_level: Optional[int] = None
    expected_location: Optional[int] = None
    @validator('expected_salary', pre=True, always=True)
    def validate_expected_salary(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('expected_job_function', pre=True, always=True)
    def validate_expected_job_function(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('expected_industry', pre=True, always=True)
    def validate_expected_industry(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('expected_job_level', pre=True, always=True)
    def validate_expected_job_level(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('expected_location', pre=True, always=True)
    def validate_expected_location(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class WorkingHistory(BaseModel):
    title: Optional[str] = None
    company: Optional[str] = None
    duration: Optional[int] = None
    current: Optional[bool] = False
    from_: Optional[str] = Field(default=None, alias="from")
    to: Optional[str] = None
    description: Optional[str] = None
    @validator('from_', pre=True, always=True)
    def validate_from(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7:
            return str(v) + "/01"
        return str(v)
    @validator('to', pre=True, always=True)
    def validate_to(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7 and v.lower() != "present":
            return str(v) + "/01"
        return str(v)


class Activity(BaseModel):
    name: Optional[str] = None
    position: Optional[str] = None
    organizer: Optional[str] = None
    from_: Optional[str] = Field(None, alias="from")
    to: Optional[str] = None
    description: Optional[str] = None
    current: Optional[bool] = False
    @validator('from_', pre=True, always=True)
    def validate_from(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7:
            return str(v) + "/01"
        return str(v)
    @validator('to', pre=True, always=True)
    def validate_to(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7 and v.lower() != "present":
            return str(v) + "/01"
        return str(v)


class Certificate(BaseModel):
    name: Optional[str] = None
    organizer: Optional[str] = None
    year: Optional[str] = None
    link: Optional[str] = None
    @validator('year', pre=True, always=True)
    def ensure_string_year(cls, v):
        if not v:
            return None
        return str(v)
    @validator('link', pre=True, always=True)
    def validate_link(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v

class Education(BaseModel):
    major: Optional[str] = None
    university: Optional[str] = None
    degree: Optional[int] = 11
    from_: Optional[str] = Field(None, alias="from")
    to: Optional[str] = None
    gpa: Optional[str] = None
    description: Optional[str] = None
    @validator('from_', pre=True, always=True)
    def validate_from(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7:
            return str(v) + "/01"
        return str(v)
    @validator('to', pre=True, always=True)
    def validate_to(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7 and v.lower() != "present":
            return str(v) + "/01"
        return str(v)
    @validator('degree', pre=True, always=True)
    def validate_degree(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class Language(BaseModel):
    name: Optional[int] = None
    rating: Optional[int] = None
    @validator('name', pre=True, always=True)
    def validate_name(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v
    @validator('rating', pre=True, always=True)
    def validate_rating(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class Reference(BaseModel):
    full_name: Optional[str] = None
    position: Optional[str] = None
    company: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    @validator('email', pre=True, always=True)
    def validate_email(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class Recommendation(BaseModel):
    recommender_name: Optional[str] = None
    position: Optional[str] = None
    company: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    linkedin: Optional[str] = None
    recommendation_paragraph: Optional[str] = None
    @validator('linkedin', pre=True, always=True)
    def validate_linkedin(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class Project(BaseModel):
    name: Optional[str] = None
    skills: Optional[List[str]] = None
    from_: Optional[str] = Field(None, alias="from")
    to: Optional[str] = None
    description: Optional[str] = None
    @validator('from_', pre=True, always=True)
    def validate_from(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7 and v.lower() != "present":
            return str(v) + "/01"
        return str(v)
    @validator('to', pre=True, always=True)
    def validate_to(cls, v):
        if not v:
            return None
        if len(v) == 4:
            return str(v) + "/01/01"
        elif len(v) == 7:
            return str(v) + "/01"
        return str(v)
    @validator('skills', pre=True, always=True)
    def validate_skills(cls, v):
        if not v:
            return None
        if v == "":
            return None
        return v


class Skill(BaseModel):
    name: Optional[str] = None
    level: Optional[int] = None
    year_of_experience: Optional[int] = None
    @validator('level', pre=True, always=True)
    def validate_level(cls, v):
        if not v:
            return None
        if v == "":
            return None
    @validator('year_of_experience', pre=True, always=True)
    def validate_year_of_experience(cls, v):
        if not v:
            return None
        if v == "":
            return None


class APIResume(BaseModel):
    model_config = ConfigDict(strict=False)
    contact_info: Optional[ContactInfo] = None
    basic_info: Optional[BasicInfo] = None
    summary: Optional[Summary] = None
    working_preference: Optional[WorkingPreference] = None
    working_histories: Optional[List[WorkingHistory]] = []
    activities: Optional[List[Activity]] = []
    certificates: Optional[List[Certificate]] = []
    educations: Optional[List[Education]] = []
    languages: Optional[List[Language]] = []
    references: Optional[List[Reference]] = []
    recommendations: Optional[List[Recommendation]] = []
    projects: Optional[List[Project]] = []
    skills: Optional[List[Skill]] = []
    resume_id: Optional[int]
    user_id: Optional[int]
    lastdateupdated: Optional[datetime]
