from datetime import datetime
from typing import List, Literal, Optional

import phonenumbers
from dateutil import parser
from langchain_core.pydantic_v1 import BaseModel, Field
from pydantic.v1 import validator


def has_numbers(input_string):
    return any(char.isdigit() for char in input_string)


class ContactInfo(BaseModel):
    name: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    phone: Optional[str] = Field(default=None)
    job_title: Optional[str] = Field(default=None)
    gender: Optional[Literal[1, 2, None]] = Field(default=None)
    marriage_status: Optional[Literal[1, 2, None]] = Field(default=None)
    address_city: Optional[int] = Field(default=None)
    address_district: Optional[str] = Field(default=None)
    address: Optional[str] = Field(default=None)
    linkedin: Optional[str] = Field(default=None)
    birthday: Optional[str] = Field(default=None)

    @validator("name", pre=True, allow_reuse=True)
    def rewrite_name(cls, field):
        if field is None:
            return field
        return field.title()

    @validator("job_title", pre=True, allow_reuse=True)
    def rewrite_job_title(cls, field):
        if field is None:
            return field
        return field.title()

    @validator("phone", pre=True, allow_reuse=True)
    def reformat_phone_number(cls, field):
        if field is None:
            return field
        region = "VN"
        try:
            result = phonenumbers.parse(field, region)
            phone_number = f"+{result.country_code}-{result.national_number}"
            return phone_number
        except phonenumbers.NumberParseException:
            return field

    @validator("linkedin", pre=True, allow_reuse=True)
    def check_linkedin(cls, field):
        if field is None:
            return field
        if "linkedin" not in field:
            return None
        return field

    @validator("birthday", pre=True, allow_reuse=True)
    def reformat_birthday(cls, field):
        try:
            if field is None:
                return field
            if field is not None:
                date = parser.parse(field)
                return date.strftime("%Y/%m/%d")
            else:
                return field
        except parser.ParserError:
            return field
        except TypeError:
            return field.strftime("%Y/%m/%d")


class BasicInfo(BaseModel):
    highest_education: Optional[int] = Field(default=None)
    years_of_experience: Optional[int] = Field(default=None)
    current_job_level: Optional[int] = Field(default=None)
    current_job_function: Optional[int] = Field(default=None)
    current_industry: Optional[int] = Field(default=None)


class Summary(BaseModel):
    objectives: Optional[str] = Field(default=None)


class WorkingPreference(BaseModel):
    expected_salary: Optional[int] = Field(default=None)
    unit: Optional[str] = Field(default=None)
    expected_job_function: Optional[int] = Field(default=None)
    expected_industry: Optional[int] = Field(default=None)
    expected_job_level: Optional[int] = Field(default=None)
    expected_location: Optional[int] = Field(default=None)

    @validator("expected_salary", pre=True, allow_reuse=True)
    def validate_expected_salary(cls, field):
        if field is None:
            return field
        if not isinstance(field, int):
            return None
        return field


class WorkingHistory(BaseModel):
    title: Optional[str] = Field(default=None)
    company: Optional[str] = Field(default=None)
    from_: Optional[str] = Field(default=None, alias="from")
    to: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    duration: Optional[int] = Field(default=None)

    @validator("from_", pre=True, allow_reuse=True)
    def reformat_from(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field

    @validator("to", pre=True, allow_reuse=True)
    def reformat_to(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field


class Activity(BaseModel):
    name: Optional[str] = Field(default=None)
    position: Optional[str] = Field(default=None)
    organizer: Optional[str] = Field(default=None)
    from_: Optional[str] = Field(default=None, alias="from")
    to: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)

    @validator("from_", pre=True, allow_reuse=True)
    def reformat_from(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field

    @validator("to", pre=True, allow_reuse=True)
    def reformat_to(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field


class Certificate(BaseModel):
    name: Optional[str] = Field(default=None)
    organizer: Optional[str] = Field(default=None)
    year: Optional[str] = Field(default=None)
    link: Optional[str] = Field(default=None)

    @validator("year", pre=True, allow_reuse=True)
    def reformat_year(cls, field):
        if field is None:
            return field
        try:
            date = parser.parse(field)
            return date.strftime("%Y")
        except parser.ParserError:
            return field


class Education(BaseModel):
    major: Optional[str] = Field(default=None)
    university: Optional[str] = Field(default=None)
    degree: Optional[int] = Field(default=None)
    from_: Optional[str] = Field(default=None, alias="from")
    to: Optional[str] = Field(default=None)
    gpa: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)

    @validator("from_", pre=True, allow_reuse=True)
    def reformat_from(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field

    @validator("to", pre=True, allow_reuse=True)
    def reformat_to(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field


class Language(BaseModel):
    name: Optional[int] = Field(default=None)
    rating: Optional[int] = Field(default=None)


class Reference(BaseModel):
    full_name: Optional[str] = Field(default=None)
    position: Optional[str] = Field(default=None)
    company: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    phone: Optional[str] = Field(default=None)

    @validator("phone", pre=True, allow_reuse=True)
    def reformat_phone_number(cls, field):
        if field is None:
            return field
        region = "VN"
        try:
            result = phonenumbers.parse(field, region)
            phone_number = f"+{result.country_code}-{result.national_number}"
            return phone_number
        except phonenumbers.NumberParseException:
            return field


class Recommendation(BaseModel):
    recommender_name: Optional[str] = Field(default=None)
    position: Optional[str] = Field(default=None)
    company: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    phone: Optional[str] = Field(default=None)
    linkedin: Optional[str] = Field(default=None)
    recommendation_paragraph: Optional[str] = Field(default=None)

    @validator("phone", pre=True, allow_reuse=True)
    def reformat_phone_number(cls, field):
        if field is None:
            return field
        region = "VN"
        try:
            result = phonenumbers.parse(field, region)
            phone_number = f"+{result.country_code}-{result.national_number}"
            return phone_number
        except phonenumbers.NumberParseException:
            return field


class Skill(BaseModel):
    name: Optional[str] = Field(default=None)
    level: Optional[int] = Field(default=None)
    year_of_experience: Optional[int] = Field(default=None)


class Project(BaseModel):
    name: Optional[str] = Field(default=None)
    skills: Optional[List[str]] = Field(default=None)
    from_: Optional[str] = Field(default=None, alias="from")
    to: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)

    @validator("from_", pre=True, allow_reuse=True)
    def reformat_from(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field

    @validator("to", pre=True, allow_reuse=True)
    def reformat_to(cls, field):
        if field is None:
            return field
        try:
            if not has_numbers(field) and field != "":
                return "now"
            date = parser.parse(field, default=datetime(2015, 1, 1))
            return date.strftime("%Y/%m")
        except parser.ParserError:
            return field


class BatchResume(BaseModel):
    contact_info: ContactInfo = Field(
        ..., description="Contact information of the resume.", title="Contact Info"
    )
    basic_info: BasicInfo = Field(
        ..., description="Basic information of the resume.", title="Basic Info"
    )
    working_preference: WorkingPreference = Field(
        ...,
        description="Working preferences of the resume.",
        title="Working Preference",
    )
    summary: Summary = Field(
        ..., description="Summary or objectives of the resume.", title="Summary"
    )
    working_histories: List[WorkingHistory] = Field(
        default_factory=list,
        description="List all information of working histories in the resume.",
        title="Working Histories",
    )
    educations: List[Education] = Field(
        default_factory=list,
        description="List all information of educations in the resume.",
        title="Educations",
    )
    skills: List[Skill] = Field(
        default_factory=list,
        description="List all information of skills and their proficiency levels in the resume.",
        title="Skills",
    )
    languages: List[Language] = Field(
        default_factory=list,
        description="List all information of languages and proficiency levels in the resume.",
        title="Languages",
    )
    references: List[Reference] = Field(
        default_factory=list,
        description="List all information  of references in the resume.",
        title="References",
    )
    certificates: List[Certificate] = Field(
        default_factory=list,
        description="List all information of certificates in the resume.",
        title="Certificates",
    )
    recommendations: List[Recommendation] = Field(
        default_factory=list,
        description="List all information of recommendations in the resume.",
        title="Recommendations",
    )
    projects: List[Project] = Field(
        default_factory=list,
        description="List all information of projects and their details in the resume.",
        title="Projects",
    )
    activities: List[Activity] = Field(
        default_factory=list,
        description="List all information of activities in the resume.",
        title="Activities",
    )
