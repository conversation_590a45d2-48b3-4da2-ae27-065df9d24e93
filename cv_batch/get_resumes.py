import asyncio
import json
import os
import time
from concurrent.futures import ThreadPoolExecutor

import aiohttp
import pymysql
import requests
from aiohttp import ClientSession
from dotenv import load_dotenv

_ = load_dotenv()


def connect_mysql_db(user, password, db):
    print(os.getenv("MYSQL_HOST_SLAVE"), os.getenv("MYSQL_PORT"))
    return pymysql.connect(
        host=os.getenv("MYSQL_HOST_SLAVE"),
        port=int(os.getenv("MYSQL_PORT")),
        user=user,
        passwd=password,
        db=db,
        charset="utf8",
    )


def get_resumes(batch_size=20):
    try:
        query = """
SELECT userid, resumeid, fileorigin, lastdateupdated FROM in_inter.tblresume_batch_extractor;
"""
        session = connect_mysql_db(
            os.getenv("MYSQL_USER"),
            os.getenv("MYSQL_PASSWORD"),
            os.getenv("MYSQL_DATABASE_INTER"),
        )
        cursor = session.cursor()
        cursor.execute(query)

        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break
            yield rows

        cursor.close()
        session.close()
    except Exception as e:
        print("Error: ", e)


def get_fake_resumes(batch_size=20):
    image_url = os.getenv("IMAGE_SERVER")
    try:
        query = f"""
SELECT re.userId, ra.resumeId, CONCAT('{image_url}/resumes_sentdirectly/',ra.folderName,'/',ra.physicalFileApp,'.',ra.extension) AS fileorigin, re.lastdateupdated
FROM tblresume AS re,tblresume_attachment AS ra 
WHERE re.resumeid = ra.resumeid AND re.resumeid in (select resumeid from tblresume where userid  in (SELECT userid  from tblregistrationinfo where username in (
    '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'
)) and isAttached = 1);
"""
        session = connect_mysql_db(
            os.getenv("MYSQL_USER"),
            os.getenv("MYSQL_PASSWORD"),
            os.getenv("MYSQL_DATABASE"),
        )
        cursor = session.cursor()
        cursor.execute(query)

        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break
            yield rows

        cursor.close()
        session.close()
    except Exception as e:
        print("Error: ", e)


def download_resumes(resumes):
    for resume in resumes:
        user_id, resume_id, link, lastdateupdated = resume
        filename = os.path.basename(link)
        extension = os.path.splitext(filename)[-1]
        download_file_path = os.path.join(os.getenv("DOWNLOAD_FOLDER"), f"{user_id}_{resume_id}{extension}")
        response = requests.get(link)
        if response.status_code == 200:
            with open(download_file_path, "wb") as f:
                f.write(response.content)
                print(f"downloaded resumeId {resume_id}")
        else:
            print("resume_id:", resume_id)
            print("Status_code:", response.status_code)


async def post_resume(session, resume_id):
    url = f"{os.getenv('AI_CV_EXTRACTOR_SERVER')}/extract"
    headers = {
        "User-Agent": "Batch process",
        "Authorization": f"Bearer {os.getenv('AI_CV_EXTRACTOR_SERVICE_TOKEN')}",
    }
    start_time = time.time()
    print(f"Processing resume_id: {resume_id}")

    try:
        async with session.post(url, headers=headers, data={"resume_id": resume_id}, timeout=120) as response:
            if response.status == 200:
                result = await response.json()
            else:
                result = {}
    except asyncio.TimeoutError:
        print(f"Timeout processing resume_id: {resume_id}")
        result = {}

    elapsed_time = time.time() - start_time
    print(f"Done resume_id: {resume_id}, took {elapsed_time:.2f} seconds")
    return resume_id, result


async def process_resume_with_metadata(session, resume):
    user_id, resume_id, _, lastdateupdated = resume
    resume_id, result = await post_resume(session, resume_id)

    if result.get("result"):
        result["result"].update({
            "resume_id": resume_id,
            "user_id": user_id,
            "lastdateupdated": lastdateupdated,
        })
        return result
    return None


async def main(resumes):
    worker_count = int(os.getenv("WORKER_COUNT", 3))
    semaphore = asyncio.Semaphore(worker_count)

    # Configure connection pooling for better worker scaling
    connector = aiohttp.TCPConnector(
        limit_per_host=int(os.getenv("MAX_CONNECTIONS_PER_HOST", worker_count)),
        limit=int(os.getenv("MAX_TOTAL_CONNECTIONS", worker_count * 2)),
        ttl_dns_cache=300,
        use_dns_cache=True,
    )

    timeout = aiohttp.ClientTimeout(total=int(os.getenv("CONNECTION_TIMEOUT", 30)))

    async def process_with_semaphore(session, resume):
        async with semaphore:
            return await process_resume_with_metadata(session, resume)

    async with ClientSession(connector=connector, timeout=timeout) as session:
        # Process resumes with semaphore to limit concurrency
        tasks = [process_with_semaphore(session, resume) for resume in resumes]
        results = await asyncio.gather(*tasks)

    # Filter out None results (failed processing)
    return [result for result in results if result is not None]


async def extract_resumes_api(resumes):
    return await main(resumes)


if __name__ == "__main__":
    download_resumes()
