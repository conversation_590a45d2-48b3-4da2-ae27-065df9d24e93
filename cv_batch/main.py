import argparse
import asyncio
import glob
import json
import os
import time

from cv_batch.get_resumes import extract_resumes_api
from services.redis.redis import rd

from .get_results import api_insert_to_es, get_batch_job_results, insert_to_es
from .get_resumes import download_resumes, get_fake_resumes, get_resumes
from .push_jobs import create_batch_files, create_batch_job


def remove_all_files(folder):
    for file in glob.glob(os.path.join(folder, "*")):
        try:
            os.remove(file)
        except Exception as e:
            print(f"Error removing {file}: {e}")


def process_batches(resume_batches, redis_key, glob_path):
    batch_job_ids = []
    print(resume_batches)
    for batch in resume_batches:
        download_resumes(batch)
        list_batch_file = create_batch_files(glob_path)
        for batch_file in list_batch_file:
            batch_job = create_batch_job(batch_file.id)
            batch_job_ids.append(batch_job.id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        remove_all_files("downloads")
        remove_all_files("batch-files")
    return batch_job_ids


def get_remaining_results(batch_job_ids, redis_key):
    while batch_job_ids:
        for batch_job_id in batch_job_ids[:]:
            results = get_batch_job_results(batch_job_id)
            if results:
                insert_to_es(results)
                batch_job_ids.remove(batch_job_id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        print(f"Number of remaining batches: {len(batch_job_ids)}")
        time.sleep(15)


async def process_api(resume_batches):
    results = []
    for batch in resume_batches:
        results = await extract_resumes_api(batch)
        await api_insert_to_es(results)
    return results


def main(args):
    redis_key = "attached_resume_batch_processing"
    glob_path = "downloads/*.*"

    if int(args.API) == 1:
        batch_size = int(args.Batch_Size)
        resume_batches = get_fake_resumes(batch_size) if int(args.Test) == 1 else get_resumes(batch_size)
        asyncio.run(process_api(resume_batches))
    else:
        if int(args.Resumed_Run) == 0:
            batch_size = int(args.Batch_Size)
            resume_batches = get_fake_resumes(batch_size) if int(args.Test) == 1 else get_resumes(batch_size)
            batch_job_ids = process_batches(resume_batches, redis_key, glob_path)
            print("Push Job Done")
        else:
            batch_job_ids = json.loads(rd.get(redis_key) or "[]")

        get_remaining_results(batch_job_ids, redis_key)
        print("Get Results Done")


# if __name__ == "__main__":
#     BASE_PATH = os.path.dirname(os.path.abspath(__file__))
#     batch_files_folder = os.path.join(BASE_PATH, "batch-files")
#     logs_files_folder = os.path.join(BASE_PATH, "logs")
#     downloads_folder = os.path.join(BASE_PATH, "downloads")
#     results_folder = os.path.join(BASE_PATH, "results")
#     file_contents_folder = os.path.join(BASE_PATH, "file_contents")
#     os.makedirs(batch_files_folder, exist_ok=True)
#     os.makedirs(logs_files_folder, exist_ok=True)
#     os.makedirs(results_folder, exist_ok=True)
#     os.makedirs(file_contents_folder, exist_ok=True)
#     os.makedirs(downloads_folder, exist_ok=True)

#     parser = argparse.ArgumentParser()
#     parser.add_argument("-a", "--Action_Type", required=True, help="Action Type")
#     parser.add_argument("-b", "--Batch_Size", required=True, help="Batch Size")
#     parser.add_argument("-r", "--Resumed_Run", required=True, help="Resumed Run")
#     parser.add_argument("-t", "--Test", required=True, help="Test")
#     args = parser.parse_args()

#     main(args)
