{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"notebookRunGroups": {"groupValue": "2"}}, "outputs": [], "source": ["import glob\n", "import json\n", "import os\n", "\n", "from dotenv import load_dotenv\n", "from langfuse.openai import OpenAI\n", "from openai import BadRequestError\n", "\n", "from prompt import prompt\n", "from model import Resume\n", "from utils import get_content, num_tokens_from_messages\n", "\n", "_ = load_dotenv()\n", "\n", "\n", "failed_folder = os.path.join(\"tests\", \"failed\")\n", "passed_folder = os.path.join(\"tests\", \"passed\")\n", "folder_logs = os.path.join(passed_folder, \"logs\")\n", "folder_outputs = os.path.join(passed_folder, \"outputs\")\n", "os.makedirs(folder_logs, exist_ok=True)\n", "os.makedirs(folder_outputs, exist_ok=True)\n", "client = OpenAI()\n", "\n", "\n", "def extract(file):\n", "    TOKEN_LIMIT = 128000\n", "    MAX_TOKENS = 16383\n", "\n", "    user_id, resume_id = os.path.splitext(os.path.basename(file))[0].split(\"_\")\n", "\n", "    if os.path.exists(os.path.join(folder_outputs, f\"{user_id}_{resume_id}.json\")):\n", "        return\n", "\n", "    content = get_content(file)\n", "\n", "    with open(f\"{folder_logs}/{user_id}_{resume_id}.log\", \"w\") as f:\n", "        f.write(content)\n", "\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": prompt},\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": content,\n", "        },\n", "    ]\n", "\n", "    prompt_token = num_tokens_from_messages(messages, model=\"gpt-4o-mini\")\n", "    if prompt_token + MAX_TOKENS > TOKEN_LIMIT:\n", "        MAX_TOKENS = TOKEN_LIMIT - prompt_token\n", "    if MAX_TOKENS < 1024:\n", "        print(\"Content in file is too long for parse\")\n", "        return\n", "    try:\n", "        completion = client.chat.completions.create(\n", "            model=\"gpt-4o-mini\",\n", "            response_format={\"type\": \"json_object\"},\n", "            temperature=0.0,\n", "            messages=messages,\n", "        )\n", "    except BadRequestError as e:\n", "        print(\"Error:\", e)\n", "        return\n", "    try:\n", "        output = json.loads(completion.choices[0].message.content)\n", "        output = Resume(**output).dict(by_alias=True)\n", "        with open(f\"{folder_outputs}/{user_id}_{resume_id}.json\", \"w\") as f:\n", "            f.write(json.dumps(output, indent=4, ensure_ascii=False))\n", "\n", "        print(\"Finished user_id {} - resume_id {}\".format(user_id, resume_id))\n", "    except Exception as e:\n", "        print(\"Error:\", e)\n", "        return\n", "\n", "\n", "def run_one_file():\n", "    filename = \"5600747_8037395.pdf\"\n", "\n", "    re_test = True\n", "\n", "    if not re_test:\n", "        file = os.path.join(failed_folder, filename)\n", "    else:\n", "        file = os.path.join(passed_folder, filename)\n", "    extract(file)\n", "\n", "\n", "def run_multi_files():\n", "    for file in glob.glob(os.path.join(\"downloads\", \"*.*\")):\n", "        print(\"file:\", os.path.basename(file))\n", "        extract(file)\n", "\n", "\n", "# run_one_file()\n", "run_multi_files()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_successed = [\n", "    os.path.splitext(os.path.basename(file))[0]\n", "    for file in glob.glob(os.path.join(folder_outputs, \"*.json\"))\n", "]\n", "list_processed = [\n", "    os.path.splitext(os.path.basename(file))[0]\n", "    for file in glob.glob(os.path.join(folder_logs, \"*.log\"))\n", "]\n", "\n", "\n", "list_failed = [item for item in list_processed if item not in list_successed]\n", "list_failed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, json, glob\n", "from datetime import datetime\n", "\n", "from dotenv import load_dotenv\n", "from elasticsearch import Elasticsearch\n", "\n", "from utils import check_resume_exists, get_elasticsearch_mapping, update_status_resume\n", "\n", "_ = load_dotenv()\n", "\n", "es = Elasticsearch(\n", "    \"{}:{}\".format(os.getenv(\"ELASTICSEARCH_HOST\"), os.getenv(\"ELASTICSEARCH_PORT\")),\n", "    http_auth=(os.getenv(\"ELASTICSEARCH_USER\"), os.getenv(\"ELASTICSEARCH_PASSWORD\")),\n", ")\n", "\n", "index = \"{}_{}\".format(\n", "    os.getenv(\"ELASTICSEARCH_INDEX\"), datetime.now().strftime(\"%Y.%m.%d\")\n", ")\n", "# mapping = get_elasticsearch_mapping(Resume)\n", "\n", "with open(\"mapping.json\", \"r\") as f:\n", "    mapping = json.loads(f.read())\n", "\n", "if not es.indices.exists(index=index):\n", "    es.indices.create(index=index, body=mapping)\n", "    \n", "passed_folder = os.path.join(\"tests\", \"passed\")\n", "folder_outputs_2610 = os.path.join(passed_folder, \"outputs\")\n", "\n", "\n", "for file in glob.glob(os.path.join(folder_outputs_2610, \"*.json\")):\n", "    user_id, resume_id = os.path.splitext(os.path.basename(file))[0].split(\"_\")\n", "    with open(file, \"r\") as f:\n", "        response = json.loads(f.read())\n", "    data = {\n", "        \"@timestamp\": datetime.utcnow().strftime(\"%Y-%m-%dT%H:%M:%S.%fZ\"),\n", "        \"userId\": int(user_id),\n", "        \"resumeId\": int(resume_id),\n", "        \"result\": response,\n", "    }\n", "    if not check_resume_exists(resume_id):\n", "        parsingStatus = 0\n", "        description = \"\"\n", "        try:\n", "            es.index(\n", "                index=index,\n", "                document=data,\n", "            )\n", "            parsingStatus = 1\n", "            print(\"Added user id {} and resume id {} to es\".format(user_id, resume_id))\n", "        except Exception as e:\n", "            print(\"Error:\", e)\n", "            description = str(e)\n", "            parsingStatus = 2\n", "        now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "        update_status_resume(\n", "            user_id=user_id,\n", "            resume_id=resume_id,\n", "            parsingStatus=parsingStatus,\n", "            description=description,\n", "            created_on=now,\n", "            updated_on=now,\n", "        )\n", "    else:\n", "        print(\"resume id {} is exists\".format(resume_id))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dev-3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 2}