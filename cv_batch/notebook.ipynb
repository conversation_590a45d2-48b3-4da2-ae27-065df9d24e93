{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Batch processing with the Batch API\n", "\n", "The new Batch API allows to **create async batch jobs for a lower price and with higher rate limits**.\n", "\n", "Batches will be completed within 24h, but may be processed sooner depending on global usage. \n", "\n", "Ideal use cases for the Batch API include:\n", "\n", "- Tagging, captioning, or enriching content on a marketplace or blog\n", "- Categorizing and suggesting answers for support tickets\n", "- Performing sentiment analysis on large datasets of customer feedback\n", "- Generating summaries or translations for collections of documents or articles\n", "\n", "and much more!\n", "\n", "This cookbook will walk you through how to use the Batch API with a couple of practical examples.\n", "\n", "We will start with an example to categorize movies using `gpt-3.5-turbo`, and then cover how we can use the vision capabilities of `gpt-4-turbo` to caption images.\n", "\n", "Please note that multiple models are available through the Batch API, and that you can use the same parameters in your Batch API calls as with the Chat Completions endpoint."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating the batch file\n", "\n", "The batch file, in the `jsonl` format, should contain one line (json object) per request.\n", "Each request is defined as such:\n", "\n", "```\n", "{\n", "    \"custom_id\": <REQUEST_ID>,\n", "    \"method\": \"POST\",\n", "    \"url\": \"/v1/chat/completions\",\n", "    \"body\": {\n", "        \"model\": <MODEL>,\n", "        \"messages\": <MESSAGES>,\n", "        // other parameters\n", "    }\n", "}\n", "```\n", "\n", "Note: the request ID should be unique per batch. This is what you can use to match results to the initial input files, as requests will not be returned in the same order."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import json\n", "\n", "from openai import OpenAI\n", "from count_token_messages import num_tokens_from_messages\n", "from schema import Resume, get_prompt_from_model\n", "from dotenv import load_dotenv\n", "\n", "_ = load_dotenv()\n", "\n", "client = OpenAI()\n", "\n", "PROMPT = get_prompt_from_model(Resume)\n", "MODEL = \"gpt-3.5-turbo\"\n", "TOKEN_LIMIT = 200000\n", "TEMPERATURE = 0\n", "\n", "\n", "def create_tasks(glob_path):\n", "    tasks = []\n", "    files = glob.glob(glob_path)\n", "    total_token = 0\n", "    for index, file in enumerate(files, 1):\n", "        response = get_content(file)\n", "        if response.status_code == 200:\n", "            content = response.json()[\"text\"]\n", "            content = process_content(content)\n", "            if len(content) > int(os.getenv(\"MIN_CONTENT_LENGTH\")):\n", "                with open(\"log/{}.log\".format(index), \"w\") as f:\n", "                    f.write(str(content))\n", "                    print(\"Written content file userId {} to file\".format(index))\n", "                messages = [\n", "                    {\"role\": \"system\", \"content\": PROMPT},\n", "                    {\"role\": \"user\", \"content\": content},\n", "                ]\n", "                total_token += num_tokens_from_messages(messages, MODEL)\n", "                if total_token > TOKEN_LIMIT:\n", "                    break\n", "                task = {\n", "                    \"custom_id\": f\"task-{index}\",\n", "                    \"method\": \"POST\",\n", "                    \"url\": \"/v1/chat/completions\",\n", "                    \"body\": {\n", "                        # This is what you would have in your Chat Completions API call\n", "                        \"model\": MODEL,\n", "                        \"temperature\": TEMPERATURE,\n", "                        \"response_format\": {\"type\": \"json_object\"},\n", "                        \"messages\": [\n", "                            {\"role\": \"system\", \"content\": PROMPT},\n", "                            {\"role\": \"user\", \"content\": content},\n", "                        ],\n", "                    },\n", "                }\n", "                tasks.append(task)\n", "            else:\n", "                response = get_content(file, ocr=True)\n", "                if response.status_code == 200:\n", "                    content = response.json()[\"text\"]\n", "                    content = process_content(content)\n", "                    with open(\"log/{}.log\".format(index), \"w\") as f:\n", "                        f.write(str(content))\n", "                        print(\"Written content file userId {} to file\".format(index))\n", "\n", "                    messages = [\n", "                        {\"role\": \"system\", \"content\": PROMPT},\n", "                        {\"role\": \"user\", \"content\": content},\n", "                    ]\n", "                    total_token += num_tokens_from_messages(messages, MODEL)\n", "                    if total_token > TOKEN_LIMIT:\n", "                        break\n", "                    task = {\n", "                        \"custom_id\": f\"task-{index}\",\n", "                        \"method\": \"POST\",\n", "                        \"url\": \"/v1/chat/completions\",\n", "                        \"body\": {\n", "                            # This is what you would have in your Chat Completions API call\n", "                            \"model\": MODEL,\n", "                            \"temperature\": TEMPERATURE,\n", "                            \"response_format\": {\"type\": \"json_object\"},\n", "                            \"messages\": [\n", "                                {\"role\": \"system\", \"content\": PROMPT},\n", "                                {\"role\": \"user\", \"content\": content},\n", "                            ],\n", "                        },\n", "                    }\n", "                    tasks.append(task)\n", "                else:\n", "                    print(\"Status code textract:\", response.status_code)\n", "                    print(\"Reason:\", response.reason)\n", "        else:\n", "            print(\"Status code textract:\", response.status_code)\n", "            print(\"Reason:\", response.reason)\n", "    return tasks\n", "\n", "\n", "def create_batch_file(file_name, tasks):\n", "    with open(file_name, \"w\") as file:\n", "        for obj in tasks:\n", "            file.write(json.dumps(obj) + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating the batch file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["glob_path = \"./downloads/*.*\"\n", "file_name = \"batch_tasks.jsonl\"\n", "\n", "tasks = create_tasks(glob_path)\n", "create_batch_file(file_name, tasks)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Uploading the file to OpenAI and creating the batch job"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_file = client.files.create(\n", "  file=open(file_name, \"rb\"),\n", "  purpose=\"batch\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_job = client.batches.create(\n", "  input_file_id=batch_file.id,\n", "  endpoint=\"/v1/chat/completions\",\n", "  completion_window=\"24h\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checking batch status\n", "\n", "Note: this can take up to 24h, but it will usually be completed faster.\n", "\n", "You can continue checking until the status is 'completed'.\n", "\n", "```python\n", "Batch(id='batch_TEuKgKR8jL51oafLCzgZXOmp', completion_window='24h', created_at=1718798018, endpoint='/v1/chat/completions', input_file_id='file-TeEIlwDg4WkdPbs0Pn0df9aK', object='batch', status='completed', cancelled_at=None, cancelling_at=None, completed_at=1718798067, error_file_id=None, errors=None, expired_at=None, expires_at=1718884418, failed_at=None, finalizing_at=1718798066, in_progress_at=1718798019, metadata=None, output_file_id='file-xyJucpIhiDmb737aE8YBVIyD', request_counts=BatchRequestCounts(completed=4, failed=0, total=4))\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_file_name = \"batch_job_results.jsonl\"\n", "\n", "\n", "def get_batch_job_results(batch_job):\n", "    results = []\n", "    batch_job_retrieve = client.batches.retrieve(batch_job.id)\n", "    if batch_job_retrieve.status == \"completed\":\n", "        print(\n", "            \"Batch job id {} is completed with batch request counts {}\".format(\n", "                batch_job_retrieve.id, batch_job_retrieve.request_counts.completed\n", "            )\n", "        )\n", "        result_file_id = batch_job_retrieve.output_file_id\n", "        result = client.files.content(result_file_id).content\n", "        \n", "        with open(result_file_name, \"wb\") as file:\n", "            file.write(result)\n", "\n", "        with open(result_file_name, \"r\") as file:\n", "            for line in file:\n", "                # Parsing the JSON string into a dict and appending to the list of results\n", "                json_object = json.loads(line.strip())\n", "                results.append(json_object)\n", "    else:\n", "        print(\"Batch job is not completed yet!\")\n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get results from batch job "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = get_batch_job_results(batch_job)\n", "\n", "for result in results:\n", "    content = result[\"response\"][\"body\"][\"choices\"][0][\"message\"][\"content\"]\n", "    try:\n", "        resume = Resume(**json.loads(content))\n", "        with open(\"results/{}.json\".format(result[\"id\"]), \"w\") as f:\n", "            f.write(json.dumps(resume.dict(), indent=2, ensure_ascii=False))\n", "    except Exception as e:\n", "        print(\"Error:\", e)\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dev-3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 2}