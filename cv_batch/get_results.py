import glob
import json
import logging
import os
import time
import uuid
from datetime import datetime

from dotenv import load_dotenv
from elasticsearch import Elasticsearch
from openai import OpenAI
from pydantic import ValidationError

from cv_batch import *

# from services.redis.redis import rd
from services.log import *
from services.redis import *

_ = load_dotenv()

client = OpenAI()

es = Elasticsearch(
    "{}:{}".format(os.getenv("ELASTICSEARCH_HOST"), os.getenv("ELASTICSEARCH_PORT")),
    basic_auth=(os.getenv("ELASTICSEARCH_USER"), os.getenv("ELASTICSEARCH_PASSWORD")),
)

BASE_PATH = os.path.dirname(os.path.abspath(__file__))

batch_files_folder = os.path.join(BASE_PATH, "batch-files")
logs_files_folder = os.path.join(BASE_PATH, "logs")
results_folder = os.path.join(BASE_PATH, "results")

os.makedirs(batch_files_folder, exist_ok=True)
os.makedirs(logs_files_folder, exist_ok=True)
os.makedirs(results_folder, exist_ok=True)

# BASE_PATH = os.path.dirname(os.path.abspath(__file__))
# logs_files_folder = os.path.join(BASE_PATH, "logs")
# os.makedirs(logs_files_folder, exist_ok=True)
logger = log.init_log("logs/log.log")

MODEL = "gpt-4o-mini"
TEMPERATURE = 0
MAX_FILES = 10


def get_batch_job_results(batch_job_id):
    print(f"Checking...Batch job id {batch_job_id}")
    results = []
    batch_job_retrieve = client.batches.retrieve(batch_job_id)
    if batch_job_retrieve.status == "completed":
        print(f"Batch job id {batch_job_retrieve.id} is completed")
        result_file_id = batch_job_retrieve.output_file_id
        result = client.files.content(result_file_id).content
        result_path = os.path.join(results_folder, f"{batch_job_retrieve.id}.jsonl")
        with open(result_path, "wb") as file:
            file.write(result)

        with open(result_path, "r") as file:
            for line in file:
                json_object = json.loads(line.strip())
                results.append(json_object)
    else:
        print(f"Batch job id {batch_job_id}  is not completed yet!")
        return None

    return results


def insert_to_es(results):
    index = "{}_{}".format(os.getenv("ELASTICSEARCH_INDEX"), datetime.now().strftime("%Y.%m.%d"))
    mapping = get_elasticsearch_mapping(BatchResume)

    if not es.indices.exists(index=index):
        es.indices.create(index=index, body=mapping)

    total_token_prompt = 0
    total_token_completion = 0

    for result in results:
        user_id, resume_id = result["custom_id"].split("_")
        status_code = result["response"]["status_code"]
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if status_code == 200:
            content = result["response"]["body"]["choices"][0]["message"]["content"]
            total_token_prompt += result["response"]["body"]["usage"]["prompt_tokens"]
            total_token_completion += result["response"]["body"]["usage"]["completion_tokens"]
            try:
                response = json.loads(content)
                response = BatchResume(**response).dict(by_alias=True)
                data = {
                    "@timestamp": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "userId": int(user_id),
                    "resumeId": int(resume_id),
                    "result": response,
                }
            except Exception as e:
                print("Validation failed:", e)
                logger.setLevel(logging.ERROR)
                logger.error(json.dumps({"resume_id": resume_id, "error": str(e), "content": content}))
                data = {
                    "@timestamp": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "userId": int(user_id),
                    "resumeId": int(resume_id),
                    "result": None,
                }
            if not check_resume_exists(resume_id):
                parsingStatus = 0
                description = ""
                try:
                    es.index(
                        index=index,
                        document=data,
                    )
                    parsingStatus = 1
                except Exception as e:
                    description = str(e)
                    parsingStatus = 2
                update_status_resume(
                    user_id=user_id,
                    resume_id=resume_id,
                    parsingStatus=parsingStatus,
                    description=description,
                    created_on=now,
                    updated_on=now,
                )
            else:
                print("resume id {} is exists".format(resume_id))
        else:
            error = result["error"]
            update_status_resume(
                user_id=user_id,
                resume_id=resume_id,
                parsingStatus=2,
                description=error,
                created_on=now,
                updated_on=now,
            )
    return total_token_prompt, total_token_completion


def custom_serializer(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()  # Converts to ISO 8601 string format
    raise TypeError(f"Type {obj.__class__.__name__} not serializable")


async def api_insert_to_es(results):
    index = "{}_{}".format(os.getenv("ELASTICSEARCH_INDEX"), datetime.now().strftime("%Y.%m.%d"))
    mapping = get_elasticsearch_mapping(APIResume)

    if not es.indices.exists(index=index):
        es.indices.create(index=index, body=mapping)

    for result in results:
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if result:
            content = result["result"]
            user_id = result["result"]["user_id"]
            resume_id = result["result"]["resume_id"]
            try:
                response = APIResume(**content).dict(by_alias=True)
                data = {
                    "@timestamp": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "userId": int(user_id),
                    "resumeId": int(resume_id),
                    "result": response,
                }
            except Exception as e:
                print("Validation failed:", e)
                logger.setLevel(logging.ERROR)
                logger.error(
                    json.dumps(
                        {"resume_id": resume_id, "error": str(e), "content": content},
                        default=custom_serializer,
                    )
                )
                data = {
                    "@timestamp": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "userId": int(user_id),
                    "resumeId": int(resume_id),
                    "result": None,
                }
            if not check_resume_exists(resume_id):
                parsingStatus = 0
                description = ""
                try:
                    es.index(
                        index=index,
                        document=data,
                    )
                    parsingStatus = 1
                    print(f"parsingStatus: {parsingStatus}")
                except Exception as e:
                    description = str(e)
                    print(description)
                    parsingStatus = 2
                update_status_resume(
                    user_id=user_id,
                    resume_id=resume_id,
                    parsingStatus=parsingStatus,
                    description=description,
                    created_on=now,
                    updated_on=now,
                )
            else:
                print("resume id {} is exists".format(resume_id))
        else:
            error = result["error"]
            update_status_resume(
                user_id=user_id,
                resume_id=resume_id,
                parsingStatus=2,
                description=error,
                created_on=now,
                updated_on=now,
            )


if __name__ == "__main__":
    redis_key = "attached_resume_batch_processing"
    batch_job_ids = rd.get(redis_key)
    batch_job_ids = json.loads(batch_job_ids)
    while len(batch_job_ids) > 0:
        for batch_job_id in batch_job_ids[:]:
            results = get_batch_job_results(batch_job_id)
            if results is not None:
                insert_to_es(results)
                batch_job_ids.remove(batch_job_id)
        rd.set(redis_key, json.dumps(batch_job_ids))
        print("Number of remain batch: " + str(len(batch_job_ids)))
        time.sleep(15)
    print("Get Results Done")

# if __name__ == "__main__":
#     for filename in os.listdir(results_folder):
#         results = []
#         with open(os.path.join(results_folder, filename), "r", encoding="utf-8", errors="ignore") as file:
#             for line in file:
#                 try:
#                     json_object = json.loads(line.strip())
#                     results.append(json_object)
#                 except json.JSONDecodeError as e:
#                     logger.setLevel(logging.ERROR)
#                     logger.error(json.dumps({"file_name": filename, "error": str(e), "content": line.strip()}))
#                     print(f"Skipping invalid JSON in file {filename}")
#         insert_to_es(results)
