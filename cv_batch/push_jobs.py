import glob
import json
import os
import sys
import time
import uuid
from datetime import datetime

from dotenv import load_dotenv
from elasticsearch import Elasticsearch
from openai import OpenAI
from cv_batch import *
from services.redis.redis import rd

# # Get the directory containing the module
# module_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'services'))
# # Add the directory to sys.path
# sys.path.append(module_path)


_ = load_dotenv()

client = OpenAI()

es = Elasticsearch(
    "{}:{}".format(os.getenv("ELASTICSEARCH_HOST"), os.getenv("ELASTICSEARCH_PORT")),
    basic_auth=(os.getenv("ELASTICSEARCH_USER"), os.getenv("ELASTICSEARCH_PASSWORD")),
)

BASE_PATH = os.path.dirname(os.path.abspath(__file__))

batch_files_folder = os.path.join(BASE_PATH, "batch-files")
file_content_folder = os.path.join(BASE_PATH, "file_contents")
results_folder = os.path.join(BASE_PATH, "results")

os.makedirs(batch_files_folder, exist_ok=True)
os.makedirs(file_content_folder, exist_ok=True)
os.makedirs(results_folder, exist_ok=True)

client = OpenAI()

MODEL = "gpt-4o-mini"
TEMPERATURE = 0
MAX_FILES = 20


def create_task(file):
    filename = os.path.basename(file)
    user_id, resume_id = os.path.splitext(filename)[0].split("_")
    content_file = os.path.join(file_content_folder, f"{user_id}_{resume_id}.log")

    if not os.path.exists(
        os.path.join(file_content_folder, f"{user_id}_{resume_id}.log")
    ):
        content = get_content(file)
        with open(content_file, "w") as f:
            f.write(str(content))
            print(
                "Written content file userId {} with resumeId {} to file".format(
                    user_id, resume_id
                )
            )
    else:
        with open(content_file, "r") as f:
            content = f.read()

    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": content},
    ]
    prompt_token = num_tokens_from_messages(messages, MODEL)
    task = {
        "custom_id": f"{user_id}_{resume_id}",
        "method": "POST",
        "url": "/v1/chat/completions",
        "body": {
            # This is what you would have in your Chat Completions API call
            "model": MODEL,
            "temperature": TEMPERATURE,
            "response_format": {"type": "json_object"},
            "messages": messages,
        },
    }
    return task, prompt_token


def create_batch_file(tasks):
    file_name = str(uuid.uuid4())
    with open(os.path.join(batch_files_folder, f"{file_name}.jsonl"), "w") as file:
        for obj in tasks:
            file.write(json.dumps(obj) + "\n")
    return file_name


def upload_batch_file(file_name):
    batch_file = client.files.create(
        file=open(os.path.join(batch_files_folder, f"{file_name}.jsonl"), "rb"),
        purpose="batch",
    )
    return batch_file


def create_batch_job(batch_file_id):
    batch_job = client.batches.create(
        input_file_id=batch_file_id,
        endpoint="/v1/chat/completions",
        completion_window="24h",
    )
    return batch_job


def create_batch_files(glob_path):
    list_batch_file = []
    files = glob.glob(glob_path)
    tasks = []
    total_files = 0
    for file in files:
        print("Processing:", file)
        task, num_token = create_task(file)
        if task is None and num_token is None:
            continue
        tasks.append(task)
        total_files += 1
        if total_files == MAX_FILES:
            file_name = create_batch_file(tasks)
            batch_file = upload_batch_file(file_name)
            list_batch_file.append(batch_file)
            print("num_files:", len(tasks))
            tasks = []
            total_files = 0
    if len(tasks) > 0:
        print("num_files:", len(tasks))
        file_name = create_batch_file(tasks)
        batch_file = upload_batch_file(file_name)
        list_batch_file.append(batch_file)
    return list_batch_file


if __name__ == "__main__":
    redis_key = "attached_resume_batch_processing"
    batch_job_ids = []
    glob_path = "downloads/*.*"
    list_batch_file = create_batch_files(glob_path)
    for batch_file in list_batch_file:
        batch_file_id = batch_file.id
        batch_job = create_batch_job(batch_file_id)
        batch_job_ids.append(batch_job.id)
        rd.set(redis_key, json.dumps(batch_job_ids))
    print("Push Job Done")
