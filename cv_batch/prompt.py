import json

with open("cv_batch/schema.json", "r") as f:
    json_schema = json.loads(f.read())

prompt = """You are an expert at reading resumes and extracting information. The input text is formatted like a resume and includes sections such as contact info, basic info, working preference, summary, work history, education, skills, languages, references, certificates, recommendations, projects, and activities. Each section is separated by a tab, line break, or multiple spaces. Information within a section is not separated by multiple spaces.

Follow these guidelines:
1. Extract and organize all information into their respective sections.
2. Preserve the original text from the resume.
3. Perform grammar and spelling checks, fixing any errors found.
4. Remove any violent language if present.

Ensure that the extracted information adheres to the following JSON schema:
```
{}
```
""".format(
    json.dumps(json_schema)
)


