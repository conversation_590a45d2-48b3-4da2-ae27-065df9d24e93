{"definitions": {"ContactInfo": {"properties": {"name": {"default": null, "description": "The full name of the resume.", "title": "Name", "type": "string"}, "email": {"default": null, "description": "The email address listed in the resume.", "title": "Email", "type": "string"}, "phone": {"default": null, "description": "The phone number listed in the resume.", "title": "Phone", "type": "string"}, "job_title": {"default": null, "description": "The current job title of the resume.", "title": "Job Title", "type": "string"}, "gender": {"description": "The gender of the individual, where male is represented as 1 and female as 2", "enum": [1, 2], "title": "Gender", "type": "integer", "default": null}, "marriage_status": {"description": "The marital status of the individual, where single is represented as 1 and married as 2.", "enum": [1, 2], "title": "Marriage Status", "type": "integer", "default": null}, "address_city": {"description": "The current address city of the resume living. Use the corresponding numerical ID from the following list: <PERSON>:70; <PERSON>:24; <PERSON>:29; <PERSON>:28; <PERSON>:17; <PERSON>:15; <PERSON> Ria - Vung Tau:3; <PERSON>:2; <PERSON><PERSON>:5; <PERSON><PERSON>:4; <PERSON><PERSON>:6; <PERSON><PERSON>:7; <PERSON>:8; <PERSON><PERSON>:10; <PERSON><PERSON>:11; <PERSON><PERSON>:12; <PERSON><PERSON>:13; <PERSON><PERSON>:14; <PERSON>:16; <PERSON><PERSON>:18; <PERSON><PERSON>:73; Mekong Delta:71; <PERSON><PERSON>:69; <PERSON>:19; <PERSON>:20; <PERSON><PERSON>:21; <PERSON>:22; <PERSON>:23; <PERSON>:26; <PERSON>:27; <PERSON><PERSON>:72; <PERSON><PERSON>:30; <PERSON>:32; <PERSON><PERSON>:33; <PERSON><PERSON>:61; <PERSON><PERSON>:34; <PERSON>:35; <PERSON>:36; <PERSON>:37; <PERSON>:38; <PERSON>:39; <PERSON>:40; <PERSON><PERSON>:41; <PERSON><PERSON>:42; <PERSON><PERSON>:43; <PERSON><PERSON>:44; <PERSON><PERSON>:45; <PERSON><PERSON>:46; <PERSON><PERSON>:47; <PERSON><PERSON>:48; <PERSON><PERSON>:49; <PERSON><PERSON>:50; <PERSON><PERSON>:51; <PERSON>:52; <PERSON><PERSON>:53; <PERSON>h:54; <PERSON>uy<PERSON>:55; <PERSON>h <PERSON>a:56; <PERSON>hua <PERSON>hi<PERSON>e:57; T<PERSON>:58; <PERSON>ra <PERSON>h:59; <PERSON>yen <PERSON><PERSON>:60; <PERSON>h <PERSON>:62; <PERSON>h <PERSON><PERSON>:63; <PERSON>n <PERSON>:65. <PERSON> address city does not be provided, return null.", "title": "Address City", "type": "integer", "default": null}, "address_district": {"default": null, "description": "The district of residence of the resume mentioned.", "title": "Address District", "type": "string"}, "address": {"default": null, "description": "The address of the resume mentioned.", "title": "Address", "type": "string"}, "linkedin": {"default": null, "description": "The linkedIn profile URL of the resume mentioned.", "title": "Linkedin", "type": "string"}, "birthday": {"default": null, "description": "The birthday of the resume mentioned. Format date following yyyy/mm/dd.", "title": "Birthday", "type": "string"}}, "title": "ContactInfo", "description": "Contact info section in the resume", "type": "object", "required": ["name", "email", "phone", "job_title", "gender", "marriage_status", "address_city", "address_district", "address", "linkedin", "birthday"]}, "BasicInfo": {"properties": {"years_of_experience": {"default": null, "description": "The total number of years of experience mentioned in the resume. Return the number of years as an integer.", "title": "Years Of Experience", "type": "integer"}, "current_job_level": {"default": null, "description": "The current job level mentioned in the resume based on years of experience and based on job title of the latest working company. Use the corresponding numerical ID from the following list: Intern/Student:8, Fresher/Entry level:1, Experienced (non-manager):5, Manager:7, Director and above:3.", "title": "Current Job Level", "type": "integer"}, "current_job_function": {"description": "The current job function based on the latest title and working company of the resume. Use the corresponding numerical ID from the following list: Educational Consulting:1, Educational Management:2, Research and Fellowship:3, Student Services:4, Teaching/Training:5, Accounts Payable:6, Accounts Receivable:7, Audit:8, Corporate Planning/Corporate Advisory:9, Cost Accounting:10, Financial Accounting:11, Financial Managers & Controllers:12, General Accounting:13, Management Accounting:14, Taxation:15, Revenue Accounting:16, Agriculture/Livestock/Fishery:17, Architecture Design/Architectural Drafting:18, Civil Engineering:19, Project Development/Contracts:20, Health, Safety & Environment:21, Interior Design:22, Project Management:23, Urban Design & Planning:24, Business/System Analysis:25, Database Administration:26, Data Engineer/Data Analyst/AI:27, Digital Transformation:28, Hardware Developer:29, IT Management:30, IT Support/Help Desk:31, System/Cloud/DevOps Engineer:32, IT Product Management/Project Management:33, QA/QC/Software Testing:34, Security:35, Software Developer:36, Telecommunications:37, UX/UI Design:38, Customer Service:39, Customer Service - Call Center:40, Customer Service - Customer Facing:41, Fashion Design/Jewelry Design:42, Graphic Design:43, Industrial Design/Technical Design:44, Video Editing:45, Aviation Services:46, Public Transport & Taxi Services:47, Rail & Maritime Transport:48, Road Transport:49, Automotive Engineering:50, Biotechnologies:51, Chemical Engineering:52, Electrical/Electronic:53, Environmental:54, Food Technology:55, Mechanical & HVAC:56, Mining/Oil/Gas:57, Power/Water/Waste Engineering:58, Accounts & Relationship Management:59, Financial Analysis & Research:60, Client Services:61, Compliance & Risk:62, Credit:63, Debt Collection:64, Funds Management:65, Investment:66, Stockbroking & Trading:67, Bar/Beverage/Waiting:68, Chefs/Cooks:69, F&B Management:70, Employee Engagement:71, Compensation & Benefit:72, General HR:73, Performance & Career Management:74, Training and Development:75, Recruitment:76, Fleet Management:77, Freight/Cargo Forwarding:78, Import/Export & Customs:79, Procurement & Inventory:80, Supply Chain Management:81, Warehouse & Distribution:82, Actuary:83, Claims:84, Risk Consulting:85, Underwriting:86, Front Office & Guest Services:87, Hotel Reservation:88, Housekeeping:89, Tour Operator:90, Tourist Guides:91, Travel Agents:92, Banking/Finance/Commercial Law:93, Construction Law:94, Generalists:95, Employment/Retirement Law:96, Intellectual/Property Law:97, Law Clerks & Paralegals:98, Legal Practice Management:99, Legal Secretaries:100, Tax Law:101, Agency Account Management:102, Brand Management:103, Content Marketing/Copywriter:104, Digital Marketing:105, Event Management:106, Market Research & Analysis:107, Marketing:108, Product Marketing:109, Public Relations & Corporate Affairs:110, Trade Marketing:111, Art Direction/Photography:112, Printing & Publishing Services:113, Programming & Production:114, Doctor/General Practitioners/Residents:115, Medical Technologist:116, Nursing:117, Pharmacist:118, Psychology, Counselling & Social Work:119, Administration:120, General Coordinator:121, Interpreter/Translator:122, Office Management:123, Purchasing:124, Receptionist:125, Sales Administration:126, Secretary:127, Safe Guard:128, Sales/Business Development:129, Sales Engineer/Technical Sales:130, Telesales:131, Auto Mechanic:132, CNC Technician:133, Maintenance Technician:134, Printing Technician:135, Real Estate Analysis:136, Facilities Management:137, Commercial Sales, Leasing & Property Management:138, Residential Leasing & Property Management:139, Property Development:140, Valuation:141, Area/Multisite Management:142, Buying:143, Retail Assistant:144, Store Management:145, NGO/Non-Profit:146, Policy, Planning & Regulation:147, Merchandising:148, Clothing Technology:149, Assembly & Process Work:150, Machine Operator:151, Production Analysis:152, Production Operation & Planning:153, Quality Assurance/Quality Control/Quality Management:154, Research & Development:155, Pharmacy Distribution:156, CEO:157, General Management:158.", "title": "Current Job Function", "type": "integer", "default": null}, "highest_education": {"description": "The highest education level mentioned in the resume. Use the corresponding numerical ID from the following list: High school:2; Associate’s degree:3; College:12; Bachelor:4; Master:5; Doctorate:6.", "title": "Highest Education", "type": "integer", "default": null}, "current_industry": {"description": "The current industry based on the latest working company and the objectives of the resume. Use the corresponding numerical ID from the following list: Accommodation/Restaurant/Hotel/Tourism:1, Accounting/Auditing:2, Agriculture/Forestry/Aquaculture:3, Animal Health:4, Architecture/Interior Design:5, Art/Amusement/Entertainment:6, Automatic:7, Automotive:8, Banking:9, Beauty (Cosmetics) & Personal Care:10, Building Materials:11, Chemical/Biochemical:12, Construction Engineering/Infrastructure:13, E-commerce:14, Education/Training:15, Electrical/Electronics:16, Environment/Waste Services:17, Fashion/Jewelry:18, Finance:19, FMCG:20, Furniture/Wood:21, Government & NGO:22, Import/Export:23, Insurance:24, IT Software/SaaS:25, IT System & Devices:26, Labor Supply:27, Law/Legal Services:28, Logistics/Forwarding:29, Manufacturing:30, Mechanic/Machinery/Industrial Equipment:31, Media/Newspaper/Advertising:32, Medical Devices:33, Medical Services/Healthcare:34, Mining/Oil and Gas:35, Packaging/Printing/Labeling:36, Pharmaceutical:37, Plastic & Rubbers:38, Production and Distribution of Electricity/Gas/Water:39, Real Estate/Rental/Leasing:40, Research:41, Retail/Wholesale Trading:42, Securities:43, Supply Chains:44, Telecommunication:45, Textiles/Garments/Footwear:46, Transportation:47, Warehouse/Freight Yard:48.", "title": "Current Industry", "type": "integer", "default": null}}, "title": "BasicInfo", "type": "object", "required": ["years_of_experience", "current_job_level", "current_job_function", "highest_education"]}, "WorkingPreference": {"properties": {"expected_salary": {"default": null, "description": "The expected salary for the job, extracted from the resume.", "title": "Expected <PERSON><PERSON>", "type": "integer"}, "unit": {"default": null, "description": "The currency unit of the expected salary, extracted from the resume.", "title": "Unit", "type": "string"}, "expected_industry": {"description": "The expected industry based on the latest working company and the objectives of the resume. Use the corresponding numerical ID from the following list: Accommodation/Restaurant/Hotel/Tourism:1, Accounting/Auditing:2, Agriculture/Forestry/Aquaculture:3, Animal Health:4, Architecture/Interior Design:5, Art/Amusement/Entertainment:6, Automatic:7, Automotive:8, Banking:9, Beauty (Cosmetics) & Personal Care:10, Building Materials:11, Chemical/Biochemical:12, Construction Engineering/Infrastructure:13, E-commerce:14, Education/Training:15, Electrical/Electronics:16, Environment/Waste Services:17, Fashion/Jewelry:18, Finance:19, FMCG:20, Furniture/Wood:21, Government & NGO:22, Import/Export:23, Insurance:24, IT Software/SaaS:25, IT System & Devices:26, Labor Supply:27, Law/Legal Services:28, Logistics/Forwarding:29, Manufacturing:30, Mechanic/Machinery/Industrial Equipment:31, Media/Newspaper/Advertising:32, Medical Devices:33, Medical Services/Healthcare:34, Mining/Oil and Gas:35, Packaging/Printing/Labeling:36, Pharmaceutical:37, Plastic & Rubbers:38, Production and Distribution of Electricity/Gas/Water:39, Real Estate/Rental/Leasing:40, Research:41, Retail/Wholesale Trading:42, Securities:43, Supply Chains:44, Telecommunication:45, Textiles/Garments/Footwear:46, Transportation:47, Warehouse/Freight Yard:48.", "title": "Expected Industry", "type": "integer", "default": null}, "expected_job_function": {"description": "The expected job function based on the latest title and working company and the objectives of the resume. Use the corresponding numerical ID from the following list: Educational Consulting:1, Educational Management:2, Research and Fellowship:3, Student Services:4, Teaching/Training:5, Accounts Payable:6, Accounts Receivable:7, Audit:8, Corporate Planning/Corporate Advisory:9, Cost Accounting:10, Financial Accounting:11, Financial Managers & Controllers:12, General Accounting:13, Management Accounting:14, Taxation:15, Revenue Accounting:16, Agriculture/Livestock/Fishery:17, Architecture Design/Architectural Drafting:18, Civil Engineering:19, Project Development/Contracts:20, Health, Safety & Environment:21, Interior Design:22, Project Management:23, Urban Design & Planning:24, Business/System Analysis:25, Database Administration:26, Data Engineer/Data Analyst/AI:27, Digital Transformation:28, Hardware Developer:29, IT Management:30, IT Support/Help Desk:31, System/Cloud/DevOps Engineer:32, IT Product Management/Project Management:33, QA/QC/Software Testing:34, Security:35, Software Developer:36, Telecommunications:37, UX/UI Design:38, Customer Service:39, Customer Service - Call Center:40, Customer Service - Customer Facing:41, Fashion Design/Jewelry Design:42, Graphic Design:43, Industrial Design/Technical Design:44, Video Editing:45, Aviation Services:46, Public Transport & Taxi Services:47, Rail & Maritime Transport:48, Road Transport:49, Automotive Engineering:50, Biotechnologies:51, Chemical Engineering:52, Electrical/Electronic:53, Environmental:54, Food Technology:55, Mechanical & HVAC:56, Mining/Oil/Gas:57, Power/Water/Waste Engineering:58, Accounts & Relationship Management:59, Financial Analysis & Research:60, Client Services:61, Compliance & Risk:62, Credit:63, Debt Collection:64, Funds Management:65, Investment:66, Stockbroking & Trading:67, Bar/Beverage/Waiting:68, Chefs/Cooks:69, F&B Management:70, Employee Engagement:71, Compensation & Benefit:72, General HR:73, Performance & Career Management:74, Training and Development:75, Recruitment:76, Fleet Management:77, Freight/Cargo Forwarding:78, Import/Export & Customs:79, Procurement & Inventory:80, Supply Chain Management:81, Warehouse & Distribution:82, Actuary:83, Claims:84, Risk Consulting:85, Underwriting:86, Front Office & Guest Services:87, Hotel Reservation:88, Housekeeping:89, Tour Operator:90, Tourist Guides:91, Travel Agents:92, Banking/Finance/Commercial Law:93, Construction Law:94, Generalists:95, Employment/Retirement Law:96, Intellectual/Property Law:97, Law Clerks & Paralegals:98, Legal Practice Management:99, Legal Secretaries:100, Tax Law:101, Agency Account Management:102, Brand Management:103, Content Marketing/Copywriter:104, Digital Marketing:105, Event Management:106, Market Research & Analysis:107, Marketing:108, Product Marketing:109, Public Relations & Corporate Affairs:110, Trade Marketing:111, Art Direction/Photography:112, Printing & Publishing Services:113, Programming & Production:114, Doctor/General Practitioners/Residents:115, Medical Technologist:116, Nursing:117, Pharmacist:118, Psychology, Counselling & Social Work:119, Administration:120, General Coordinator:121, Interpreter/Translator:122, Office Management:123, Purchasing:124, Receptionist:125, Sales Administration:126, Secretary:127, Safe Guard:128, Sales/Business Development:129, Sales Engineer/Technical Sales:130, Telesales:131, Auto Mechanic:132, CNC Technician:133, Maintenance Technician:134, Printing Technician:135, Real Estate Analysis:136, Facilities Management:137, Commercial Sales, Leasing & Property Management:138, Residential Leasing & Property Management:139, Property Development:140, Valuation:141, Area/Multisite Management:142, Buying:143, Retail Assistant:144, Store Management:145, NGO/Non-Profit:146, Policy, Planning & Regulation:147, Merchandising:148, Clothing Technology:149, Assembly & Process Work:150, Machine Operator:151, Production Analysis:152, Production Operation & Planning:153, Quality Assurance/Quality Control/Quality Management:154, Research & Development:155, Pharmacy Distribution:156, CEO:157, General Management:158. If not provided, return null.", "title": "Expected Job Function", "type": "integer", "default": null}, "expected_job_level": {"default": null, "description": "The expected job level based on the latest job title and the objectives of the resume. Use the corresponding numerical ID from the following list: Intern/Student:8, Fresher/Entry level:1, Experienced (non-manager):5, Manager:7, Director and above:3. If not provided, return the current job level ID.", "title": "Expected Job Level", "type": "integer"}, "expected_location": {"default": null, "description": "The expected location based on current location and the latest working company of the resume. Use the corresponding numerical ID from the following list: International:70, <PERSON>:24, <PERSON>:29, <PERSON>:28, <PERSON>:17, <PERSON>:15, <PERSON> Ria <PERSON> V<PERSON> Tau:3, <PERSON>:2, <PERSON><PERSON>:5, <PERSON><PERSON>:4, <PERSON><PERSON>:6, <PERSON><PERSON>:7, <PERSON>:8, <PERSON><PERSON>:10, <PERSON><PERSON>:11, <PERSON><PERSON>:12, <PERSON><PERSON>:13, <PERSON><PERSON>:14, <PERSON>:16, <PERSON><PERSON>:18, <PERSON><PERSON>:73, Mekong Delta:71, <PERSON><PERSON>:69, <PERSON>:19, <PERSON>:20, <PERSON><PERSON>:21, <PERSON>:22, <PERSON>:23, <PERSON>:26, <PERSON>:27, <PERSON><PERSON>:72, <PERSON><PERSON>:30, <PERSON>:32, <PERSON><PERSON>:33, <PERSON><PERSON>:61, <PERSON><PERSON>:34, <PERSON>:35, <PERSON>:36, <PERSON>:37, <PERSON>:38, <PERSON>:39, <PERSON>:40, <PERSON><PERSON>:41, <PERSON><PERSON>:42, <PERSON><PERSON>:43, <PERSON><PERSON>:44, <PERSON><PERSON>:45, <PERSON><PERSON>:46, <PERSON><PERSON>:47, <PERSON><PERSON>:48, <PERSON><PERSON>:49, <PERSON><PERSON>:50, <PERSON><PERSON>:51, <PERSON>52, <PERSON><PERSON> <PERSON><PERSON>:53, <PERSON> <PERSON><PERSON>:54, <PERSON> <PERSON><PERSON>:55, <PERSON><PERSON><PERSON>:56, <PERSON><PERSON> <PERSON><PERSON><PERSON>:57, <PERSON><PERSON><PERSON>:58, <PERSON><PERSON><PERSON>:59, <PERSON> <PERSON><PERSON>:60, <PERSON><PERSON>:62, <PERSON><PERSON> <PERSON><PERSON>:63, <PERSON><PERSON>:65, <PERSON>:66. <PERSON> not provided, return null", "title": "Expected Location", "type": "integer"}}, "title": "WorkingPreference", "type": "object", "required": ["expected_salary", "unit", "expected_industry", "expected_job_function", "expected_job_level", "expected_location"]}, "Summary": {"properties": {"objectives": {"default": null, "description": "Extract all information in the summary of the resume.", "title": "Objectives", "type": "string"}}, "title": "Summary", "type": "object", "required": ["objectives"]}, "WorkingHistory": {"properties": {"company": {"default": null, "description": "The company name mentioned the working history in the working history section or working history column.", "title": "Working History Company Name", "type": "string"}, "title": {"default": null, "description": "The job title from the working history in the resume.", "title": "Working History Title", "type": "string"}, "from": {"default": null, "description": "The start date of the working history in the resume.", "title": "Working History From", "type": "string"}, "to": {"default": null, "description": "The end date of the working history in the resume.", "title": "Working History To", "type": "string"}, "description": {"default": null, "description": "Extract all information in the description of the working history in the resume. You generate this information in a nicer format by putting a bullet point in front of each line with <ul><li></li></ul> HTML format.", "title": "Working History Description", "type": "string"}, "duration": {"default": null, "description": "The duration of the working history in the resume, calculated by year.", "title": "Working History Duration", "type": "integer"}}, "title": "WorkingHistory", "description": "Working history section in the resume", "type": "object", "required": ["company", "title", "from", "to", "description", "duration"]}, "Education": {"properties": {"major": {"default": null, "description": "The major of education in the resume.", "title": "Education Major", "type": "string"}, "university": {"default": null, "description": "The university of education in the resume.", "title": "Education University", "type": "string"}, "degree": {"default": null, "description": "The degree obtained from education in the resume, represented by an ID. Possible values are: High school:2; Associate’s degree:3; College:12; Bachelor:4; Master:5; Doctorate:6; Others:11.", "title": "Education Degree", "type": "integer"}, "from": {"default": null, "description": "The start date of education in the resume.", "title": "Education From", "type": "string"}, "to": {"default": null, "description": "The end date of education in the resume.", "title": "Education To", "type": "string"}, "gpa": {"default": null, "description": "The GPA of education in the resume with its scale.", "title": "Education GPA", "type": "string"}, "description": {"default": null, "description": "Extract all information in description of the education in the resume. You generate this information in a nicer format by putting a bullet point in front of each line with <ul><li></li></ul> HTML format.", "title": "Education Description", "type": "string"}}, "title": "Education", "description": "Education section in the resume", "type": "object", "required": ["major", "university", "degree", "from", "to", "gpa", "description"]}, "Skill": {"properties": {"name": {"default": null, "description": "The name of the skill in the resume.", "title": "Skill Name", "type": "string"}, "level": {"default": null, "description": "The proficiency level of the skill in the resume. Possible values range from 1 (basic) to 5 (expert).", "title": "Skill Level", "type": "integer"}, "year_of_experience": {"default": null, "description": "The number of years of experience with the skill in the resume, represented as an integer.", "title": "Skill Year Of Experience", "type": "integer"}}, "title": "Skill", "description": "Skill section in the resume", "type": "object", "required": ["name", "level", "year_of_experience"]}, "Language": {"properties": {"name": {"default": null, "description": "The id of the language in the resume, represented by an ID. Possible values are: Arabic:1; Bengali:2; Bulgarian:3; Burmese:4; Cambodian:5; Cebuano:6; Chinese (Cantonese):7; Chinese (Mandarin):8; Czech:9; Danish:10; Dutch:11; English:12; Finnish:13; French:14; German:15; Greek:16; Hindi:17; Hungarian:18; Indonesian:19; Italian:20; Japanese:21; Javanese:22; Korean:23; Laotian:24; Malay:25; Norwegian:26; Polish:27; Portuguese:28; Romanian:29; Russian:30; Spanish:31; Swedish:32; Tagolog:33; Taiwanese:34; Thai:35; Turkish:36; Ukranian:37; Vietnamese:38; Other:39.", "title": "Language Name", "type": "integer"}, "rating": {"default": null, "description": "The proficiency level of the language in the resume, represented by an ID. Possible values are: Beginning:1; Intermediate:2; Advance:3; Native:4.", "title": "Language Rating", "type": "integer"}}, "title": "Language", "description": "Language section in the resume", "type": "object", "required": ["name", "rating"]}, "Reference": {"properties": {"full_name": {"default": null, "description": "The full name of the reference in the resume.", "title": "Reference Full Name", "type": "string"}, "position": {"default": null, "description": "The position of the reference in the resume.", "title": "Reference Position", "type": "string"}, "company": {"default": null, "description": "The company of the reference in the resume.", "title": "Reference Company", "type": "string"}, "email": {"default": null, "description": "The email address of the reference in the resume.", "title": "Reference Email", "type": "string"}, "phone": {"default": null, "description": "The phone number of the reference in the resume.", "title": "Reference Phone", "type": "string"}}, "title": "Reference", "description": "Reference section in the resume", "type": "object", "required": ["full_name", "position", "company", "email", "phone"]}, "Certificate": {"properties": {"name": {"default": null, "description": "The name of the certificate in the resume.", "title": "Certificate Name", "type": "string"}, "organizer": {"default": null, "description": "The organizer of the certificate in the resume.", "title": "Certificate Organizer", "type": "string"}, "year": {"default": null, "description": "The year the certificate was awarded.", "title": "Certificate Year", "type": "string"}, "link": {"default": null, "description": "A link to the certificate in the resume.", "title": "Certificate Link", "type": "string"}}, "title": "Certificate", "description": "Certificate section in the resume", "type": "object", "required": ["name", "organizer", "year", "link"]}, "Recommendation": {"properties": {"recommender_name": {"default": null, "description": "The name of the recommender in the recommendation of the resume.", "title": "Recommender Name", "type": "string"}, "position": {"default": null, "description": "The position of the recommender in the recommendation of the resume.", "title": "Recommendation Position", "type": "string"}, "company": {"default": null, "description": "The company of the recommender in the recommendation of the resume.", "title": "Recommendation Company", "type": "string"}, "email": {"default": null, "description": "The email address of the recommender in the recommendation of the resume.", "title": "Recommendation Email", "type": "string"}, "phone": {"default": null, "description": "The phone number of the recommender in the recommendation of the resume.", "title": "Recommendation Phone", "type": "string"}, "linkedin": {"default": null, "description": "The LinkedIn profile link of the recommender in the recommendation of the resume.", "title": "Recommendation Linkedin", "type": "string"}, "recommendation_paragraph": {"default": null, "description": "The extracted paragraph or description provided by the recommender in the recommendation of the resume. Format it nicely by placing a bullet point in front of each line.", "title": "Recommendation Paragraph", "type": "string"}}, "title": "Recommendation", "description": "Recommendation section in the resume", "type": "object", "required": ["recommender_name", "position", "company", "email", "phone", "linkedin", "recommendation_paragraph"]}, "Project": {"properties": {"name": {"default": null, "description": "The name of the project in the resume.", "title": "Project Name", "type": "string"}, "description": {"default": null, "description": "Extract all information in description of the project in the resume. You generate this information in a nicer format by putting a bullet point in front of each line with <ul><li></li></ul> HTML format.", "title": "Project Description", "type": "string"}, "skills": {"default": null, "description": "A list of skills related to the project in the resume.", "items": {"type": "string"}, "title": "Project Skills", "type": "array"}, "from": {"default": null, "description": "The start date of the project in the resume", "title": "Project From", "type": "string"}, "to": {"default": null, "description": "The end date of the project in the resume.", "title": "Project To", "type": "string"}}, "title": "Project", "description": "Project section in the resume", "type": "object", "required": ["name", "description", "skills", "from", "to"]}, "Activity": {"properties": {"name": {"default": null, "description": "The name of the activity in the resume.", "title": "Name of Activity", "type": "string"}, "position": {"default": null, "description": "The position held during the activity in the resume.", "title": "Position of Activity", "type": "string"}, "organization": {"default": null, "description": "The organization of the activity in the resume.", "title": "Organizer of Activity", "type": "string"}, "from": {"default": null, "description": "The start date of the activity in the resume.", "title": "From of Activity", "type": "string"}, "to": {"default": null, "description": "The end date of the activity in the resume.", "title": "To of Activity", "type": "string"}, "description": {"default": null, "description": "Extract all information in description of the activity in the resume. You generate this information in a nicer format by putting a bullet point in front of each line with <ul><li></li></ul> HTML format in this section.", "title": "Description of Activity", "type": "string"}}, "title": "Activity", "description": "Activity section in the resume", "type": "object", "required": ["name", "position", "organization", "from", "to", "description"]}}, "type": "object", "title": "Extract information in the provided text", "properties": {"contact_info": {"allOf": [{"$ref": "#/definitions/ContactInfo"}], "description": "Contact information section of the resume.", "title": "Contact Info"}, "basic_info": {"allOf": [{"$ref": "#/definitions/BasicInfo"}], "description": "Basic information section of the resume.", "title": "Basic Info"}, "working_preference": {"allOf": [{"$ref": "#/definitions/WorkingPreference"}], "description": "Working preferences section of the resume.", "title": "Working Preference"}, "summary": {"allOf": [{"$ref": "#/definitions/Summary"}], "description": "Summary or objectives section of the resume.", "title": "Summary"}, "working_histories": {"description": "List all information of section working histories in the resume.", "items": {"$ref": "#/definitions/WorkingHistory"}, "title": "Working Histories", "type": "array", "default": []}, "educations": {"description": "List all information of section educations in the resume.", "items": {"$ref": "#/definitions/Education"}, "title": "Educations", "type": "array", "default": []}, "skills": {"description": "List all skill of the resume provided in the skills section.", "items": {"$ref": "#/definitions/Skill"}, "title": "Skills", "type": "array", "default": []}, "languages": {"description": "List all information of section languages and proficiency levels in the resume.", "items": {"$ref": "#/definitions/Language"}, "title": "Languages", "type": "array", "default": []}, "references": {"description": "List all information  of references in the resume.", "items": {"$ref": "#/definitions/Reference"}, "title": "References", "type": "array", "default": []}, "certificates": {"description": "List all information of section certificates in the resume.", "items": {"$ref": "#/definitions/Certificate"}, "title": "Certificates", "type": "array", "default": []}, "recommendations": {"description": "List all information of section recommendations in the resume.", "items": {"$ref": "#/definitions/Recommendation"}, "title": "Recommendations", "type": "array", "default": []}, "projects": {"description": "List all information of section projects and their details in the resume.", "items": {"$ref": "#/definitions/Project"}, "title": "Projects", "type": "array", "default": []}, "activities": {"description": "List all information of section activities in the resume.", "items": {"$ref": "#/definitions/Activity"}, "title": "Activities", "type": "array", "default": []}}, "required": ["contact_info", "basic_info", "working_preference", "summary", "working_histories", "educations", "skills", "languages", "references", "certificates", "recommendations", "projects", "activities"]}