import logging as logging
import os
from logging.handlers import TimedRotatingFileHandler

# 'when' and 'interval' parameters combination defines period
# when new log file create.
# In this example new file will create every minute
# (interval=1, when='m').

os.makedirs("logs", exist_ok=True)
screening_logs_files_folder = os.path.join("screening_batch", "logs")
cv_logs_files_folder = os.path.join("cv_batch", "logs")
os.makedirs(screening_logs_files_folder, exist_ok=True)
os.makedirs(cv_logs_files_folder, exist_ok=True)


def init_log(log_path):
    rotation_logging_handler = TimedRotatingFileHandler(
        log_path, when="midnight", backupCount=7
    )
    rotation_logging_handler.suffix = "%Y-%m-%d"
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    rotation_logging_handler.setFormatter(formatter)

    logger = logging.getLogger()
    logger.addHandler(rotation_logging_handler)

    return logger
