import os

import pymysql
from dbutils.pooled_db import PooledDB
from dotenv import load_dotenv

load_dotenv()
# Set up the connection pool
pool = PooledDB(
    creator=pymysql,  # Use pymysql as the connection creator
    maxconnections=5,  # Maximum number of connections in the pool
    mincached=5,  # Start with 2 idle connections in the pool
    maxcached=10,  # Maximum number of idle connections in the pool
    blocking=True,  # If True, wait for a connection instead of throwing an error
    host=os.getenv("MYSQL_HOST"),
    port=int(os.getenv("MYSQL_PORT")),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE_INTER"),
    charset="utf8",
)

write_pool = PooledDB(
    creator=pymysql,  # Use pymysql as the connection creator
    maxconnections=5,  # Maximum number of connections in the pool
    mincached=5,  # Start with 2 idle connections in the pool
    maxcached=10,  # Maximum number of idle connections in the pool
    blocking=True,  # If True, wait for a connection instead of throwing an error
    host=os.getenv("MYSQL_HOST"),
    port=int(os.getenv("MYSQL_PORT")),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE"),
    charset="utf8",
)

slave_pool = PooledDB(
    creator=pymysql,  # Use pymysql as the connection creator
    maxconnections=5,  # Maximum number of connections in the pool
    mincached=5,  # Start with 2 idle connections in the pool
    maxcached=10,  # Maximum number of idle connections in the pool
    blocking=True,  # If True, wait for a connection instead of throwing an error
    host=os.getenv("MYSQL_HOST_SLAVE"),
    port=int(os.getenv("MYSQL_PORT")),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE_INTER"),
    charset="utf8",
)


def get_connection(write=False, slave=False):
    connection_pool = pool
    if write:
        connection_pool = write_pool
    if slave:
        connection_pool = slave_pool

    return connection_pool.connection()


def execute_query(query, write=True):
    conn = get_connection(write=write)
    try:
        with conn.cursor() as cursor:
            cursor.execute(query)
            result = cursor.fetchall()
        conn.commit()
    finally:
        conn.close()

    return result


def execute_query_with_dict_cursor(query, write=True):
    conn = get_connection(write=write)
    try:
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(query)
            result = cursor.fetchall()
        conn.commit()
    finally:
        conn.close()

    return result
